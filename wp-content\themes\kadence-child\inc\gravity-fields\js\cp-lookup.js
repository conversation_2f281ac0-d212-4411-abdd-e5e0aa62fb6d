/**
 * CP & Company Name AJAX Lookup
 *
 * Handles live lookup for CP Number and Company Name fields via AJAX.
 *
 * @package Kadence Child
 * <AUTHOR>  
 * @since   1.0.0
 */

( function( $, window, document ) {
    'use strict';

    /**
     * Debounce timers for AJAX calls.
     *
     * @type {number}
     */
    var cpTimer,
        companyTimer;

    /**
     * Delay (in milliseconds) before triggering AJAX for CP Number.
     *
     * @type {number}
     */
    var CP_DELAY = 300;

    /**
     * Delay (in milliseconds) before triggering AJAX for Company Name.
     *
     * @type {number}
     */
    var COMPANY_DELAY = 500;

    /**
     * Handle input event on CP Number fields.
     *
     * - Normalizes input to uppercase, enforces "CP" prefix + 7 digits.
     * - Debounces user input to avoid rapid AJAX calls.
     * - Shows a loading indicator while fetching.
     * - Injects result or error message into the results container.
     *
     * @since 1.0.0
     */
    $( document ).on( 'input', '.gfield_cp_number', function() {
        var $field   = $( this ),
            $form    = $field.closest('form'),
            $cpNumId = $form.find('.cp-number-id input'),
            $wrap    = $field.closest( '.ginput_container_cp_number' ),
            $results = $wrap.find( '.cp-number-results' ),
            $loading = $wrap.find( '.cp-number-loading' ),
            raw      = $field.val(),
            val      = raw.toUpperCase().replace( /[^A-Z0-9]/g, '' );

        // Normalize to "CP" + up to 7 digits.
        if ( val.length ) {
            // Remove all non-digits
            var digits = val.replace(/[^0-9]/g, '');

            // Limit to 7 digits
            digits = digits.substr(0, 7);

            // Final value with "CP" prefix
            //val = 'CP' + digits;

            // Update field value
            $field.val( val );
        }
        // Cap to "CP" + 7 digits = 9 characters.
        val = val.substr( 0, 9 );

        // Update field value.
        $field.val( val );

        // Clear any pending timer and empty previous results.
        clearTimeout( cpTimer );
        $results.empty();

        // Only trigger AJAX when the format is complete.
        if ( /^CP\d{7}$/.test( val ) ) {
            cpTimer = setTimeout( function() {
                $results.show();
                $loading.show();

                $.post(
                    ajax_object.ajax_url,
                    {
                        action:    'search_cp_number',
                        cp_number: val,
                        nonce:     ajax_object.nonce
                    },
                    function( resp ) {
                        $loading.hide();

                        if ( resp.success ) {
                            var d    = resp.data,
                                html = '';

                            // If the CP lookup returned boiler details, show extended info
                            if ( d.itemDescription ) {

                                html  = '<div class="cp-result">';
                                if (d.itemDescription && d.itemDescription.trim() !== ''){
                                    html += '<strong>Description:</strong> ' + d.itemDescription + '<br>';
                                }
                                if (d.partNumber && d.partNumber.trim() !== ''){
                                    html += '<strong>Part Number:</strong> ' + d.partNumber + '<br>';
                                }

                                if (d.modelName && d.modelName.trim() !== ''){
                                    html += '<strong>Model:</strong> ' + d.modelName + '<br>';
                                }

                                if (d.sizeName && d.sizeName.trim() !== ''){
                                    html += '<strong>Size:</strong> ' + d.sizeName + '<br>';
                                }

                                if (d.manufactureDate && d.manufactureDate.trim() !== ''){
                                    html += '<strong>Manufacture Date:</strong> ' + d.manufactureDate + '<br>';
                                }
                                if (d.installationDate && d.installationDate.trim() !== ''){
                                    html += '<strong>Installation Date:</strong> ' + d.installationDate + '<br>';
                                }
                                if (d.productRegistrationDate && d.productRegistrationDate.trim() !== ''){
                                    html += '<strong>Registration Date:</strong> ' + d.productRegistrationDate + '<br>';
                                }
                                

                                const hasRegistration = !!d.productRegistrationDate;
                                const hasInstall = d.installationDate && d.installationDate !== '0001-01-01T00:00:00';

                                // If the CP Number ID is present, set it in the hidden input
                                $cpNumId.val(d.cpNumberId).change();

                                // If the product is registered (installationDate present)
                                if ( hasRegistration ) {
                                    html += '<div class="cp-registered">';
                                    html += 'This product has already been registered.';
                                    html += '</div>';
                                    $form.find('input[type="submit"]').prop('disabled', true);

                                }else if ( hasInstall ) {
                                    html += '<div class="cp-registered">';
                                    html += 'This product cannot be registered online. Please contact Weil-McLain.';
                                    html += '</div>';
                                    $form.find('input[type="submit"]').prop('disabled', true);
                                }
                                else{
                                    //Enable submit button on the form
                                    $form.find('input[type="submit"]').prop('disabled', false);
                                }

                                html += '</div>';
                                 $results.html( html );
                            }
                            // Otherwise fall back to company info
                            else {
                                $results.html( '<div class="cp-error">Please enter a valid CP Number (e.g. CP1234567)</div>' );
                                //Diable submit button on the form
                                $form.find('input[type="submit"]').prop('disabled', true);

                            }

                           
                        } else {
                            $results.html( '<div class="cp-error">Please enter a valid CP Number (e.g. CP1234567)</div>' );
                            //Diable submit button on the form
                            $form.find('input[type="submit"]').prop('disabled', true);
                    }
                },
                    'json'
                ).fail( function() {
                    $loading.hide();
                    $results.html( '<div class="cp-error">Unable to search for your CP Number, please try again.</div>' );
                    //Diable submit button on the form
                    var $form = $field.closest('form');
                    $form.find('input[type="submit"]').prop('disabled', true);
                });
            }, CP_DELAY );
        }
    } );

    /**
     * Handle input event on Company Name fields.
     *
     * - Debounces user input when 3 or more characters are entered.
     * - Fetches matching companies via AJAX.
     * - Shows or hides an associated address field based on results.
     *
     * @since 1.0.0
     */
    $( document ).on( 'input', '.gform_company_name', function() {
        var $field         = $( this ),
            $wrap          = $field.closest( '.ginput_container_company_name' ),
            $results       = $wrap.find( '.company-name-results' ),
            $loading       = $wrap.find( '.company-name-loading' ),
            addressFieldId = $field.data( 'address-field' ),
            companyFieldId = $field.data( 'hidden-company-field' ),
            val            = $field.val();

        // Clear debounce timer & previous results.
        clearTimeout( companyTimer );
        $results.empty();

        // Only search when input length >= 3.
        if ( val.length >= 3 ) {
            companyTimer = setTimeout( function() {
                $results.show();
                $loading.show();

                $.post(
                    ajax_object.ajax_url,
                    {
                        action:       'search_company_name',
                        company_name: val,
                        nonce:        ajax_object.nonce
                    },
                    function( resp ) {
                        $loading.hide();

                        if ( resp.success && resp.data.length ) {
                            var html = '<div class="company-results-list">';

                            // Build result items.
                            $.each( resp.data, function( index, company ) {
                                // Store the entire company object for click‐handler
                                var dataAttr = encodeURIComponent( JSON.stringify( company ) );

                                html += '<div class="company-result-item" data-company-id="'+ company.accountId +'" data-company="' + dataAttr + '">';
                                html += '<strong>' + company.accountName + '</strong>';

                                if ( company.address ) {
                                    html += ' &ndash; ' + company.address;
                                }
                                html += '</div>';
                            } );

                            // Add default “not listed” option
                            html += '<div class="company-result-item company-not-listed">';
                            html += 'My company is not listed';
                            html += '</div>';

                            $results.html( html );

                           
                        } else {
                            // Add default “not listed” option
                            html = '<div class="company-result-item company-not-listed">';
                            html += 'My company is not listed';
                            html += '</div>';
                            $results.html( html );
                        }
                    },
                    'json'
                ).fail( function() {
                    $loading.hide();
                    $results.html( '<div class="company-error">Search failed</div>' );

                } );
            }, COMPANY_DELAY );
        } 
    } );

    /**
     * Handle click event on company result items.
     *
     * - Populates the company name field with the selected name.
     * - Clears the results list.
     * - Hides the address field.
     *
     * @since 1.0.0
     */
    $(document).on('click', '.company-result-item', function() {
        var $item          = $(this),
            $wrap          = $item.closest('.ginput_container_company_name'),
            $field         = $wrap.find('.gform_company_name'),
            addressFieldId = $field.data('address-field'),
            companyFieldId = $field.data('hidden-company-field');

        // Clear previous results
        $wrap.find('.company-name-results').empty();

        if ( $item.hasClass('company-not-listed') ) {
            // Show the hidden address field
            $('#input_' + companyFieldId).val('New').change();
            
            $('#field_' + addressFieldId).show().removeClass('gform_hidden');
            $('#input_' + addressFieldId).find(':input').prop('disabled', false);

            // Clear any stored accountId
            $field.removeData('account-id');
        } else {
            // Parse and apply real company
            var companyData = JSON.parse( decodeURIComponent( $item.data('company') ) );

            // Fill input and stash accountId
            $field.val( companyData.accountName );
            $field.data( 'account-id', companyData.accountId );

            $('#input_' + companyFieldId).val(companyData.accountId).change();
        
            // Hide the hidden address field
            $('#field_' + addressFieldId).hide().addClass('gform_hidden');
            $('#input_' + addressFieldId).find(':input').prop('disabled', true);
        }
    });

    /**
     * Handle submission of the Advantage form wrapper.
     *
     * - Intercepts the form submit event for forms with class `weilmclain-advantage-form_wrapper`.
     * - Prevents default submission.
     * - Sends AJAX request to verify the contact via the Marley API.
     * - Redirects to the appropriate portal based on the existence of the contact.
     *
     * @since 1.0.0
     */
    $(document).on('submit', 'form.weilmclain-advantage-form', function(e){

        var $form           = $(this),
            $submit         = $form.find('input[type="submit"], button[type="submit"]'),
            email           = $form.find('input[type="email"]').val(),
            notFoundUrl     = $form.find('.weilmclain-advantage-not-found input').val(),
            foundUrl        = $form.find('.weilmclain-advantage-found input').val(),
            emailIsValid    = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test( email );

        // If no email or invalid format, don't intercept—let GF do its thing
        if ( ! email || ! emailIsValid ) {
        return true;
        }
        e.preventDefault();
            
        // Call AJAX to verify contact
        $.post(
            ajax_object.ajax_url,
            {
                action: 'check_contact',
                email:  email,
                nonce:  ajax_object.nonce
            },
            function(resp) {
                
                if ( resp.success && resp.data.exists ) {
                    // Hide email field and submit button smoothly
                    $form.find('.gfield--type-email').slideUp();
                    $submit.hide();
                    // Contact exists → show success
                    $('#advlog-exists').slideDown();
                    $('#advlog-none').hide();
                    setTimeout(function(){
                        window.location.href = foundUrl;
                    }, 2000);
                }
                else if ( resp.success && ! resp.data.exists ) {
                    // Hide email field and submit button smoothly
                    $form.find('.gfield--type-email').slideUp();
                    $submit.hide();
                    // Contact missing → show failure
                    $('#advlog-none').slideDown();
                    $('#advlog-exists').hide();
                    setTimeout(function(){
                        window.location.href = notFoundUrl;
                    }, 2000);
                }
                else{
                    // Unbind this handler and re-submit
                    $form.off('submit').submit();
                }
                // else: do nothing
            },
            'json'
        ).fail(function(){
            // Unbind this handler and re-submit
            $form.off('submit').submit();
        });
    });

} )( jQuery, window, document );