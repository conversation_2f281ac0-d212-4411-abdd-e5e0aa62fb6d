jQuery(document).ready(function($) {
    const elementsToHideInitially = [
        '#searching-bar', 
        '#search--results', 
        '#search--results-b4', 
        '.after--search', 
        '#after-nav'
    ];
    
    $(elementsToHideInitially.join(', ')).fadeOut(1);

    $('#contractor-search').on('submit', function(e) {
        e.preventDefault();

        $('#the-locator').addClass('active');
        $('#search--results, #search--results-b4, .after--search').fadeOut(1);
        $('.after--nav').removeClass('active');
        $('.after--nav.listing').addClass('active');

        $('section#the-map-content').html('');

        const $results = $('#search--results');
        const $rsltsb4 = $('#search--results-b4');
        const query = $('#location').val();
        const radius = $('input[name="radius"]:checked').val();

        const extra  = [];
        $.each($("input[name=extra]:checked"), function(){
            extra.push($(this).val());
        });

        const extraD = [];
        $.each($("input[name=extra]:checked"), function(){
            extraD.push($(this).attr('data-display'));
        });
        
        $.ajax({
            type: 'POST',
            url: wmcl_ajax.ajax_url,
            data: {
                action: 'wm_contractor_search',
                security: wmcl_ajax.nonce,
                query,
                radius,
                extra
            },
            beforeSend: function() {
                $rsltsb4.fadeIn(1);
                $('.wm-locator-section .kt-inside-inner-col').addClass('bg-white');
                $('.under-content').fadeOut(1);
                $('#searching-bar').fadeIn(250);
                
                setTimeout(() => {
                    $('#searching-near').text('Searching Contractors Near ' + query);
                    $([document.documentElement, document.body]).animate({
                        scrollTop: ($("#form--bot").offset().top - 250)
                    }, 500);
                }, 251);
                
                const animation = `<div class="col-sm-12 text-center">
                    <img src="${wmcl_ajax.loading_gif}" class="loading-cl" />
                </div>`;
                $rsltsb4.html(animation);
            },
            success: function(response) {
				console.log(response);
                $rsltsb4.fadeOut(1200);
                
                setTimeout(function() {
                    $('.before--search').fadeOut(1);
                    $('#radius-choice').text(`${radius} Miles`);

                    var the_extras = 'No filters selected';
                    if (extraD.length !== 0) {
                        the_extras = '';
                        extraD.forEach(function(item) {
                            the_extras += '<button class="btn btn-primary btn--display btn-squ">' + item + '</button>';
                        });
                    }

                    $results.html(response.data.html).fadeIn(1250);
                    $('#the-extras').html(the_extras);
                }, 800);
                
                setTimeout(() => $('#after-nav').fadeIn(750), 1000);
                setTimeout(() => $('.after--search').fadeIn(750), 2000);
            },
            error: function(xhr, status, error) {
                console.error(`AJAX Error: ${status} - ${error}`);
                $('#search--results').html(`
                    <div class="col-sm-12 text-center">
                        <p>Error occurred: ${status}. Please try again.</p>
                    </div>
                `).fadeIn();
            }
        });
    });
    
    $('#contractor-search').keypress(function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('input[name=locon]').click();
            return false;
        }
    });
    
    $(window).on('load', function() {
        $('#loc-disclosure').hover(function() {
            if ($(this).attr('data-accepted') === 'false') {
                // $('#loc-dscl').modal({show: true});
                $(this).attr('data-accepted', 'true');
            }
        });
    });
    
    function scrollToContractor(id) {
        const element = document.getElementById(id);
        if (element) element.scrollIntoView();
    }
    
    $('.amloc').on('click', function(e) {
        e.preventDefault();
        scrollToContractor($(this).attr('data-cid'));
    });
    
    $('#radius-choice, .btn--display').click(function(e) {
        e.preventDefault();
    });

    $('#toggle-filters').click(function(e) {
        e.preventDefault();
        $('.after--search').fadeOut(950);
        setTimeout(() => $('.before--search').fadeIn(1250), 1000);
    });

    $('#loc-disclosure').click(function(e) {
        console.log('clicked');

        e.preventDefault();
        $('#loc-dscl').fadeIn(0);
        $('#loc-dscl').addClass('in');
        $('#loc-dscl').addClass('show');
        $('body').addClass('modal-open');

        $('<div />').appendTo('#loc-dscl').attr('class', 'modal-backdrop-disclaimer');

        $('#loc-dscl .close').on('click', function() {
            $('#loc-dscl').fadeOut(1);
            $('#loc-dscl').removeClass('in');
            $('body').removeClass('modal-open');
            $('#loc-dscl .modal-backdrop-disclaimer').remove();
        });
        
        $('#loc-dscl .modal-backdrop-disclaimer').on('click', function() {
            $('#loc-dscl .close').trigger('click');
        });
    });
});
