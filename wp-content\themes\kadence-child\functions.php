<?php
/**
 * Enqueue child styles.
 */
 
function child_enqueue_styles() {
	wp_enqueue_style( 'child-theme', get_stylesheet_directory_uri() . '/style.css', array(), null );
}
add_action( 'wp_enqueue_scripts', 'child_enqueue_styles', 15 );

function enqueue_sf_results_assets() {
	wp_enqueue_style( 'sf-results-style', get_stylesheet_directory_uri() . '/inc/search-filter/result.css', array(), '1.0' );
	wp_enqueue_script( 'sf-results-script', get_stylesheet_directory_uri() . '/inc/search-filter/result.js', array('jquery'), '1.0', true );
	wp_enqueue_style(
		'font-awesome',
		'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css',
		array(),
		'6.5.0'
	);
	// Google Fonts: Montserrat
	wp_enqueue_style(
		'montserrat-font',
		'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap',
		[],
		null
	);
}
add_action( 'wp_enqueue_scripts', 'enqueue_sf_results_assets' );

function remove_global_css() {
	remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
	remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
	remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
}
add_action('init', 'remove_global_css');

// Shortcode for dynamic year - use [year] in footer or as needed
function year_shortcode() {
  $year = date('Y');
  return $year;
}
add_shortcode('year', 'year_shortcode');

/* Update default GF error message gform_validation_message_FORMNUMBER */
add_filter( 'gform_validation_message_1', function ( $message, $form ) {
	if ( gf_upgrade()->get_submissions_block() ) {
		return $message;
	}
 
	$message = "<h2 class='gform_submission_error hide_summary'>Email address is required. Please <NAME_EMAIL> format</h2>";
   
 
	return $message;
}, 10, 2 );

//Includes
require_once get_stylesheet_directory() . '/inc/shortcodes/ecorebates.php';
require_once get_stylesheet_directory() . '/inc/shortcodes/product-download-links.php';
require_once get_stylesheet_directory() . '/inc/shortcodes/ecorebates-product.php';
require_once get_stylesheet_directory() . '/inc/shortcodes/product-comparision/product-comparision-grid.php';
require_once get_stylesheet_directory() . '/inc/shortcodes/product-comparision/compare-checkbox.php';
require_once get_stylesheet_directory() . '/inc/shortcodes/product-comparision/compare-modal.php';
require_once get_stylesheet_directory() . '/inc/search-filter/sf-functions.php';
require_once get_stylesheet_directory() . '/inc/gravity-fields/gravity-functions.php';


add_filter( 'kadence_blocks_pro_query_loop_query_vars', function( $query, $ql_query_meta, $ql_id ) {
  if (in_array('residential-training', $query['post_type'])) {
    $now = date("Ymd");

    $query['meta_query'] = [
      [
        'key' => 'end_date',
        'value' => $now,
        'compare' => '>=',
        'type' => 'NUMERIC',
      ]
    ];

	$query['meta_key'] = 'start_date';
	$query['orderby'] = 'meta_value_num';
	$query['order'] = 'ASC';
  }
  return $query;
}, 10, 3 );


// This filter allows global $post to work inside Query Loops
add_filter('render_block', function($block_content, $block) {
  if ( $block ['blockName'] === 'core/shortcode' ) {
   return do_shortcode( $block_content );
  }
  return $block_content;
 }, 20, 2);

require_once get_stylesheet_directory() . '/inc/wm-contractor-locator/wm-contractor-locator.php';
new WM_Contractor_Locator();

function wpb_discontinued_files_shortcode( $atts ) {
	global $post;

	if ( ! isset( $post ) || get_post_type( $post ) !== 'discontinued-product' ) {
		if ( isset( $atts['id'] ) ) {
			$post = get_post( intval( $atts['id'] ) );
			setup_postdata( $post );
		} else {
			return '';
		}
	}

	ob_start();

	if ( have_rows( 'documents', $post->ID ) ) {
		echo '<ul class="discontinued-files">';
		while ( have_rows( 'documents', $post->ID ) ) {
			the_row();
			$file = get_sub_field( 'file' );
			if ( $file ) {
				$url = esc_url( $file['url'] );
				$title = esc_html( $file['title'] );
				echo "<li><a href='{$url}' target='_blank' rel='noopener'>{$title}</a></li>";
			}
		}
		echo '</ul>';
	}

	return ob_get_clean();
}
add_shortcode( 'discontinued_files', 'wpb_discontinued_files_shortcode' );


add_shortcode('discontinued_products_loop', function () {
	$args = [
		'post_type'      => 'discontinued-product',
		'posts_per_page' => -1,
		'post_status'    => 'publish',
	];

	$query = new WP_Query($args);
	if (! $query->have_posts()) return '<p>No discontinued products found.</p>';

	ob_start();
	?>

	<div class="dp-archive-wrapper">
		<?php while ($query->have_posts()) : $query->the_post(); ?>
			<div class="dp-accordion">
				<button class="dp-toggle">
					<strong><?php the_title(); ?></strong><br>
					<span class="dp-category">
						<?php
						$terms = get_the_terms(get_the_ID(), 'discontinued-product-category');
						if ($terms && !is_wp_error($terms)) {
							$names = wp_list_pluck($terms, 'name');
							echo esc_html(implode(', ', $names));
						}
						?>
					</span>
				</button>
				<div class="dp-panel">
					<?php echo do_shortcode('[discontinued_files id="' . get_the_ID() . '"]'); ?>
				</div>
			</div>
		<?php endwhile; ?>
	</div>

	<?php
	wp_reset_postdata();
	return ob_get_clean();
});

add_action('template_redirect', function () {
    if (untrailingslashit($_SERVER['REQUEST_URI']) === '/products') {
        wp_redirect("/all-products", 301);
        exit;
    }
});

add_action('wp_footer', 'custom_login_redirect_js');
function custom_login_redirect_js() {
    if (is_page('account-login')) {
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const params = new URLSearchParams(window.location.search);
            const redirectTo = params.get('redirect_to');
            const isLoggedIn = document.body.classList.contains('logged-in');

            if (isLoggedIn && redirectTo) {
                const cleanedUrl = window.location.origin + window.location.pathname;
                window.history.replaceState({}, document.title, cleanedUrl);
                const finalDestination = decodeURIComponent(redirectTo);
                console.log('Redirecting to:', finalDestination);
                window.location.href = finalDestination;
            }
        });
        </script>
        <?php
    }
}


function prevent_news_prefix_for_cpts( $args, $post_type ) {
	$excluded_post_types = get_acf_custom_post_types();

    if ( in_array( $post_type, $excluded_post_types ) ) {
        $args['rewrite'] = array(
            'slug' => $args['rewrite']['slug'] ?? $post_type,
            'with_front' => false, // this prevents adding /news for CPTs
        );
    }

    return $args;
}
add_filter( 'register_post_type_args', 'prevent_news_prefix_for_cpts', 10, 2 );

function get_acf_custom_post_types() {
    $excluded_post_types = [];
    $field_groups = acf_get_field_groups();

    foreach ( $field_groups as $group ) {
        if ( isset($group['location']) && is_array($group['location']) ) {
            foreach ( $group['location'] as $location_group ) {
                foreach ( $location_group as $rule ) {
                    if ( $rule['param'] === 'post_type' && $rule['operator'] === '==' ) {
                        $post_type = $rule['value'];
                        if ( $post_type !== 'post' && !in_array($post_type, $excluded_post_types) ) {
                            $excluded_post_types[] = $post_type;
                        }
                    }
                }
            }
        }
    }

    return $excluded_post_types;
}

add_filter('show_admin_bar', function() {
    return current_user_can('administrator');
});