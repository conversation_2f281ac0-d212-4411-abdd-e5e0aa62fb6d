/**
 * CP & Company Lookup Styles
 *
 * Styles for AJAX lookup results on CP Number and Company Name fields.
 *
 * @package    Kadence Child
 * <AUTHOR> Name
 * @subpackage CSS
 * @since      1.0.0
 */

/* Results containers for CP and Company Name lookups */
.cp-number-loading,
.company-name-loading{
  /*Hidden by default */
  display:none;
}

/* Results containers for CP Number and Company Name lookups */
.cp-number-results,
.company-name-results {
  /*Hidden by default */
  display:none;
  
  /* white background for contrast */
  background-color: #fff;

  /* subtle border and rounding */
  border: 1px solid #ddd;
  border-radius: 4px;

  /* soft drop shadow */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  /* spacing and scrollable overflow */
  margin-top: 10px;
  max-height: 275px;
  overflow-y: auto;
}

/* Individual result items */
.cp-result,
.company-result-item {
  /* divider between items */
  border-bottom: 1px solid #eee;

  /* pointer to indicate clickability */
  cursor: pointer;

  /* inner padding */
  padding: 10px;
}

/* Hover state for company result items */
.company-result-item:hover {
  background-color: #f5f5f5;
}

/* Error and loading messages */
.cp-error,
.company-error,
.company-no-results,
.cp-number-loading,
.company-name-loading {
  /* italicized, muted text */
  color: #666;
  font-style: italic;

  /* match padding of result items */
  padding: 10px;
}

/* Wrapper for multiple company results */
.company-results-list {
  /* scrollable list of matches */
  max-height: 200px;
  overflow-y: auto;
}
/* Color for the required field */
.cp-registered{
  color:red;
}

/* White labels and text within Advantage form */
.gform_wrapper.gravity-theme .weilmclain-advantage-form label.gfield_label,
.gform_wrapper.gravity-theme .weilmclain-advantage-form .advlog-text {
  color: white;
  font-weight: 500;
}

/* Links inside the log text */
.gform_wrapper.gravity-theme .weilmclain-advantage-form .advlog-text a {
  text-decoration: none;
  color: var(--global-palette2);
}

/* Hover state for links in log text */
.gform_wrapper.gravity-theme .weilmclain-advantage-form .advlog-text a:hover {
  text-decoration: underline;
}

/* Hide GF validation errors in this wrapper */
.weilmclain-advantage-form_wrapper .gform_validation_errors {
  display: none;
}

/* Container for success/fail messages */
.weilmclain-advantage-form_wrapper .advlog-answr-inner {
  display: flex;
  gap: 20px;
  color: white;
}

/* Icons in the answer panel */
.weilmclain-advantage-form_wrapper .advlog-answr-inner img {
  max-width: 60px;
  max-height: 60px;
}