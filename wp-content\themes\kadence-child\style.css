/*!
Theme Name: Ka<PERSON> Child
Theme URI: https://www.kadencewp.com/kadence-theme/
Template: kadence
Author: Kadence WP
Author URI: https://www.kadencewp.com/
Description: A child theme for the Kadence Theme.
Version: 1.0.0
License: GNU General Public License v3.0 (or later)
License URI: https://www.gnu.org/licenses/gpl-3.0.html
Text Domain: kadence-child
*/


/* A11y Fixes */

/* Allow for esc key to close Kadence submenu */
.nav--toggle-sub li:hover > ul,
.nav--toggle-sub li.menu-item--toggled-on > ul,
.nav--toggle-sub li:not(.menu-item--has-toggle):focus > ul {
 display: none;
}

.main-navigation .menu-item .sub-menu {
  display: none;
}
.main-navigation .menu-item .sub-menu.submenu-open,
.main-navigation .menu-item .sub-menu.toggle-show {
  display: block;
}

/* Gravity Forms */
.gform_wrapper.gravity-theme input[type=color], .gform_wrapper.gravity-theme input[type=date], .gform_wrapper.gravity-theme input[type=datetime-local], .gform_wrapper.gravity-theme input[type=datetime], .gform_wrapper.gravity-theme input[type=email], .gform_wrapper.gravity-theme input[type=month], .gform_wrapper.gravity-theme input[type=number], .gform_wrapper.gravity-theme input[type=password], .gform_wrapper.gravity-theme input[type=search], .gform_wrapper.gravity-theme input[type=tel], .gform_wrapper.gravity-theme input[type=text], .gform_wrapper.gravity-theme input[type=time], .gform_wrapper.gravity-theme input[type=url], .gform_wrapper.gravity-theme input[type=week], .gform_wrapper.gravity-theme select, .gform_wrapper.gravity-theme textarea {
	font-size:1rem;
}
input[type="text"], input[type="email"], input[type="url"], input[type="password"], input[type="search"], input[type="number"], input[type="tel"], input[type="range"], input[type="date"], input[type="month"], input[type="week"], input[type="time"], input[type="datetime"], input[type="datetime-local"], input[type="color"], textarea {
	color: var(--global-palette3);
}
input[type="text"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="password"]:focus, input[type="search"]:focus, input[type="number"]:focus, input[type="tel"]:focus, input[type="range"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="time"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="color"]:focus, textarea:focus, .gform_wrapper.gravity-theme .gfield textarea:focus, select:focus {
	border-color:#000000;
	outline:-webkit-focus-ring-color auto 1px !important;
}
/* Styling for floating label for gravity forms. 
 * Add class of floating-label to desired form fields */
.floating-label {
        position: relative;
}

.floating-label label {
    position: absolute;
    top: 12px;
    left: 0;
    margin: 0;
    opacity: 0;
    line-height: 1.4;
    font-size: 1rem;
    transition: all ease 0.4s;
}

.floating-label:focus-within label,
.floating-label input:focus ~ label, 
.floating-label textarea:focus ~ label,
.floating-label input.valid ~ label,
.floating-label textarea.valid ~ label, 
.floating-label input:visited ~ label,
.floating-label textarea:visited ~ label,
.floating-label input:-webkit-autofill ~ label,
.floating-label textarea:-webkit-autofill ~ label,
.floating-label.input-active label {
        top: 0;
    left: 16px;
    font-size: .875rem !important;
    color: var(--global-palette3);
    opacity: 1;
}

/* Basic A11Y fixes for Kadence Blocks */
body .kt-blocks-modal-link:not(.kb-btn-global-inherit):focus-within {
	outline:inherit;
}

/* Search Bar */
.woocommerce-product-search {
	position:relative;
}
.woocommerce-product-search .screen-reader-text {
	cursor: text;
	 font-size: 1rem;
	 left: 1rem;
	 margin: 0;
	 opacity: 0;
	 padding-right: 3.5em;
	 position: absolute;
	 top: 0.75rem;
	 font-weight: 500;
	 transition: all ease 0.4s;
}
.woocommerce-product-search:focus-within .screen-reader-text {
	clip:unset;
	clip-path:unset;
	height:auto;
	width:auto;
	left: 12px;
    padding: 0 5px !important;
    background: #fff;
    font-size: 13px!important;
    top: -13px;
    left: 1px;
    line-height: 19px;
    color: #383b41;
    opacity: 1;
}
.woocommerce-product-search input[type='search']::placeholder,
.search-form input[type='search']::placeholder {
	opacity:1;
}

/* menu */
body:not(.hide-focus-outline) .header-navigation li.menu-item--has-toggle>a:focus .dropdown-nav-toggle {
	opacity:1 !important;
}
body #search-drawer .drawer-inner form ::-webkit-input-placeholder {
	opacity:1;
}
#search-drawer input:-webkit-autofill,
#search-drawer input:-webkit-autofill:focus {
    transition: background-color 0s 600000s, color 0s 600000s !important;
}

/* Cookies */
.cky-preference-header .cky-btn-close img {
	filter:brightness(0);
}

/* Kadence Slider */
.kb-splide .splide__pagination__page:focus-visible {
	outline:2px solid #000;
}

/* Smart Slider */
body .n2-ss-slider :focus-visible,
body .n2-ss-slider a.n2-ow:focus-visible, 
body .n2-ss-slider .n2-ow-all a:focus-visible {
	outline:1px solid #fff !important;
	box-shadow:inset 0 0 0 1px #000000 !important;
}
.n2-ss-slider .n2-ss-widget.n2-ss-widget-hidden {
	display:none;
}

/* Forms */
body select,
body select.orderby {
	background-image: url("data:image/svg+xml,%3Csvg aria-hidden='true' class='kadence-svg-icon kadence-arrow-down-svg' fill='currentColor' version='1.1' xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M5.293 9.707l6 6c0.391 0.391 1.024 0.391 1.414 0l6-6c0.391-0.391 0.391-1.024 0-1.414s-1.024-0.391-1.414 0l-5.293 5.293-5.293-5.293c-0.391-0.391-1.024-0.391-1.414 0s-0.391 1.024 0 1.414z'%3E%3C/path%3E%3C/svg%3E");
}

input[type="text"], input[type="email"], input[type="url"], input[type="password"], input[type="search"], input[type="number"], input[type="tel"], input[type="range"], input[type="date"], input[type="month"], input[type="week"], input[type="time"], input[type="datetime"], input[type="datetime-local"], input[type="color"], textarea, body select, body .select2-container--default .select2-selection--single, .select2-container--default .select2-selection--single .select2-selection__rendered {
	border-color:#4E5C74;
	color:var(--global-palette3);
}

/* Events Calendar */
.tribe-events .datepicker .day.focused, .tribe-events .datepicker .day:focus, .tribe-events .datepicker .day:hover, .tribe-events .datepicker .month.focused, .tribe-events .datepicker .month:focus, .tribe-events .datepicker .month:hover, .tribe-events .datepicker .year.focused, .tribe-events .datepicker .year:focus, .tribe-events .datepicker .year:hover {
	outline:2px solid #000;
}
.tribe-events-c-search__input-group {
	position:relative;
}
.tribe-events-c-search__input-group label {
	opacity:0;
	transition: all ease 0.4s;
}
.tribe-events-c-search__input-group:focus-within label {
	clip:unset;
	height:auto;
	width:auto;
	opacity:1;
	margin:0;
	top:-40px;
}
.tribe-events .datepicker .next .tribe-events-c-top-bar__datepicker-nav-icon-svg path, .tribe-events .datepicker .prev .tribe-events-c-top-bar__datepicker-nav-icon-svg path, #primary .tribe-events .tribe-common-c-btn-icon--caret-left .tribe-common-c-btn-icon__icon-svg path, #primary .tribe-events .tribe-common-c-btn-icon--caret-right .tribe-common-c-btn-icon__icon-svg path,
.tribe-events .tribe-events-c-nav__next:disabled .tribe-events-c-nav__next-icon-svg path, .tribe-events button.tribe-events-c-nav__next:disabled .tribe-events-c-nav__next-icon-svg path {
	fill:#707070;
}

/* WooCommerce */
select.orderby:focus-visible {
	outline:2px solid #000;
}
.kadence-shop-top-row {
	position:relative;
}
.kadence-shop-top-row .skip-link {
	right:0;
	left:auto;
}
span.required {
	color:red;
}
.form-description {
	font-size:1rem;
}
.kadence-product-gallery-thumbnails.splide.splide--nav>.splide__slider>.splide__track>.splide__list>.splide__slide:focus-visible,
.kb-splide .splide__arrows .splide__arrow:focus-visible{
	box-shadow:0 0 0 3px #000 !important;
	outline:2px solid #fff !important;
}
body .kb-advanced-slide-inner {
	overflow:visible;
}
/* used to display focus outline above, if needed adjust to not apply to general Kadence slider */
/*body .kb-splide .splide__list {
	padding:3px !important;
}
body .kb-splide.splide-initial .splide__list {
	gap:3px;
}*/
.gbtn.focused {
	outline: 2px solid #fff !important;
}
body .woocommerce form .form-row label {
	color:var(--global-palette3);
}
body .wp-element-button:disabled {
	opacity:0.7;
}


/* End A11y Fixes */
body sup, body sub {
    font-size: 50%;
}
body sup {
    top: -0.8em;
}
body .njt-nofi-notification-bar .njt-nofi-text {
	text-transform: uppercase;	
	display: flex;
	align-items: center;
	column-gap: 30px;
	row-gap: 10px;
}
.app-wrap {
	display: flex;
	column-gap: 15px;
}
@media (max-width:767px) {
	body .njt-nofi-notification-bar .njt-nofi-text {
		flex-direction: column;
	}
}
.force-full-width {
	width: 100%;
}
@media (min-width:1025px) and (max-width:1284px) {
	body .wp-block-kadence-navigation .menu {
		column-gap: 20px;
	}
	.wp-block-kadence-navigation .menu-container>.menu>.menu-item>.kb-link-wrap>.kb-nav-link-content {
		font-size: 0.875rem;
	}
	.wp-block-kadence-header-column-center-right .kb-nav-desktop-horizontal-layout-standard > .navigation > .menu-container > .menu {
		column-gap: 20px;
		--kb-nav-margin-right: 0;
	}
}
.underline-link.wp-block-kadence-navigation.navigation-desktop-orientation-vertical .kb-nav-link-content {
	text-decoration: underline;
}

/*Product Comparision Grid*/

.comparison-grid-wrapper {
    overflow-x: auto;
    display: flex;
}

.comparison-grid {
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	margin-top: 20px;
	border: 1px solid #ccc;
  }
  
  .comparison-row {
	display: flex;
	border-bottom: 1px solid #ddd;
  }
  
  .comparison-header {
	background-color: #f5f5f5;
	font-weight: bold;
  }
  
  .comparison-attribute {
	width: 200px;
	padding: 10px;
	background-color: #f0f0f0;
	flex-shrink: 0;
  }
  
  .comparison-product {
	flex: 1;
	padding: 10px;
	min-width: 180px;
	border-left: 1px solid #eee;
	text-align: center;
  }

.comparison-product img {
	margin: 10px auto;
}
  
.product-template-default #primary {
	margin: 0;
}
.product-template-default .site-container {
	max-width: 100%;
	padding: 0;
}
.product-template-default .single-content,
.product-template-default .entry-header{
    margin: 0;
}
.yoast-breadcrumbs {
	font-size: 1rem;
}
.yoast-breadcrumbs a {
	color: var(--global-palette3);
	text-decoration: none;
}
.yoast-breadcrumbs a::after {
	content: "";
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' xmlns='http://www.w3.org/2000/svg' aria-hidden='true'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center / contain;
	position: relative;
	top: 3px;
	left: 2px;
}
.kt-socialstyle-style_03 a {
	color: var(--global-palette1);
	border-color: var(--global-palette1);
}
.kt_simple_share_container.kt_share_shortcode.kt_share_location_none {
	width: auto;
}
body .kb-splide .splide__arrow {
	border: 0;
	opacity: 1;
}
body .kb-splide .splide__arrow--prev {
    left: -30px;
}
body .kb-splide .splide__arrow--next {
    right: -30px;
}
.modal-video-section {
    position: relative;
}
.modal-video-section .kt-blocks-modal-link {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}
.product-tech-specs .kb-table th:first-child,
.product-tech-specs .kb-table td:first-child {
    border-left: 0;
}
.product-tech-specs .kb-table tr:first-child th {
    border-top: 0;
}
.product-tech-specs .kb-table th:last-child,
.product-tech-specs .kb-table td:last-child {
    border-right: 0;
}
.product-tech-specs .kb-table th {
    text-align: left;
}

@media (max-width:767px) {
    .anchor-link-nav.wp-block-kadence-navigation {
        width: 100%;
    }
    .anchor-link-nav.wp-block-kadence-navigation .menu-container>.menu>.menu-item>.kb-link-wrap>.kb-nav-link-content {
        display: block;
    }
    .anchor-link-nav.wp-block-kadence-navigation .wp-block-kadence-navigation-link .kb-nav-item-title-wrap {
        justify-content: space-between;
    }
}

.kb-gallery-ul .kadence-blocks-gallery-item .kadence-blocks-gallery-item-inner figure .kb-gal-image-radius {
    padding: 4px;
    border: 1px solid var(--global-palette7);
}
.product-models-table-wrap {
    overflow-x: auto;
}
table.product-models-table {
    border-collapse: collapse;
}
table.product-models-table thead th {
    min-height: 98px;
    padding: 20px 18px;
    font-size: 1.25rem;
    font-weight: 500;
    text-align: left;
    color: var(--global-palette2);
    border-left: 2px solid var(--global-palette7);
    border-bottom: 2px solid var(--global-palette7);
}
table.product-models-table thead th:first-child {
    border-left: 0;
}
table.product-models-table tbody th {
    min-width: 220px;
    padding: 20px 18px;
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--global-palette1);
    border-bottom: 2px solid var(--global-palette7);
}
table.product-models-table tbody td {
    min-width: 110px;
    padding: 20px 18px;
    border-left: 2px solid var(--global-palette7);
    border-bottom: 2px solid var(--global-palette7);
}
.tech-specs-product {
    display: flex;
    align-items: center;
    column-gap: 25px;
}
.wp-block-post-template {
    margin: 0;
}
.wp-block-post-template .wp-block-post {
    margin-bottom: 20px;
}
.search-filter-field__input.search-filter-input-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}
.wp-block-query-pagination {
    display: flex;
    align-items: center;
    font-size: 1.125rem;
}
.page-numbers:not(.next, .prev) {
    display: inline-block;
    width: 24px;
    text-align: center;
}
.wp-block-query-pagination-next {
    margin-left: 20px;
}
.wp-block-query-pagination-previous {
    margin-right: 20px;
}
.product-tech-specs {
    transform: rotateX(180deg);
}
.product-tech-specs > .kb-table {
    transform: rotateX(180deg);
}
body .search-filter-icon__svg,
body .search-filter-input-radio__control>svg {
    fill: var(--global-palette4);
}
@media (max-width:767px) {
    .modal-video-section .kt-blocks-modal-link .kb-svg-icon-wrap {
        font-size: 4rem;
    }
}

/* Forms */

.login-form-wrapper .gfield input[type="text"],
.login-form-wrapper .gfield input[type="password"] {
	width: 100%;
}

/* Query loop */

.wp-block-post-template.columns-3  {
	display: grid;
	grid-template-columns: 1fr;
	column-gap: 30px;
	row-gap: 30px;
}

.wp-block-post-template.columns-3 .wp-block-post {
	margin-bottom: 0;
}

.wp-block-post-template.columns-3 .wp-block-post > .wp-block-kadence-column,
.wp-block-post-template.columns-3 .wp-block-post > .wp-block-kadence-column > .kt-inside-inner-col {
	height: 100%;
}

@media screen and (min-width: 768px) {
	.wp-block-post-template.columns-3  {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media screen and (min-width: 1200px) {
	.wp-block-post-template.columns-3  {
		grid-template-columns: repeat(3, 1fr);
	}
}

.hide-empty-tax:not(:has(.kb-dynamic-list)) {
	display: none;
}

.product-card__compare {
	margin-top: 20px;
}

.product-card__compare input[type="checkbox"] {
	opacity: 0;
	position: fixed;
	width: 0;
}

.product-card__compare label {
	display: inline-block;
	padding: 7px 14px;
	border: 2px solid var(--global-palette1);
	border-radius: 8px;
	background-color: var(--global-palette1);
	color: var(--global-palette9);
	transition: .2s all ease-in;
}

.product-card__compare label:hover {
	background-color: transparent;
	color: var(--global-palette1);
}

.product-card__compare label:focus-within {
	outline: -webkit-focus-ring-color auto 2px !important;
	outline-offset: 3px;
}

.product-card__compare label:has(input[type="checkbox"]:checked) {
	background-color: var(--global-palette5);
	border-color: var(--global-palette5);
	color: var(--global-palette9);
	opacity: .75;
}

.product-card__detail > .kt-inside-inner-col > h3,
.product-card__detail > .kt-inside-inner-col > span {
	display: inline !important;
}

/* PRODUCT DOWNLOAD LINKS */
.acf-grid-wrapper {
    display: flex;
    flex-direction: column;
}
.acf-grid {
    display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 1rem;
}
.acf-grid-cell {
    padding: 4px;
}
.acf-grid .acf-grid-cell h3 {
    margin-bottom: 24px;
    font-size: 1.125rem;
    font-weight: 400;
    text-transform: uppercase;
	font-family: var(--global-heading-font-family);
}

.acf-grid .acf-grid-cell a {
	display: block;
}

@media (max-width: 1025px) {
	.acf-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.acf-grid {
		grid-template-columns: 1fr;
	}
}

/* PRODUCT SKU BUTTON STYLING */

#ecorebates .eco-m .eco-c .eco-widget a.ecr-details {
	width: 100%;
	min-height: 65px;
	background: #65a042;
	border: 1px solid #65a042;
	border-radius: 8px;
	font-style: normal;
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.08;
    text-transform: uppercase;
	color: var(--global-palette-btn);
    padding: 10px 23px;
	align-content: center;
}

#ecorebates .eco-m .eco-c .eco-widget a.ecr-details.ecr-open-details::after {
	content: ''!important;
}

#ecorebates .eco-m .eco-c .eco-widget a.ecr-details:hover {
	background: var(--global-palette1);
    border: 1px solid var(--global-palette1);
}

@media (max-width: 960px) {
	#ecorebates {
		width: 100%;
	}
}

/* FIX FOR THE PRODUCT FILES TABLES */

.show-more-table-scroll .ninja_table_wrapper {
	transform: rotateX(180deg);
	
}

.show-more-table-scroll .ninja_table_wrapper table {
	transform: rotateX(180deg);
	overflow-x: auto;
}

/* MEGA MENU ICON FIX */

body .menu-icon-title .kadence-info-box-image-intrisic {
	padding-bottom: unset!important;
	height: auto!important;
}

/* Contact form Styling */

.gform_wrapper.gravity-theme .contact-form label.gfield_label,
.gform_wrapper.gravity-theme .contractors-form label.gfield_label{
	font-weight: 400;
	text-transform: uppercase;
}

.gform_wrapper.gravity-theme .contact-form input[type="submit"],
.gform_wrapper.gravity-theme .contractors-form input[type="submit"]{
	font-weight: 400;
	min-width: 200px;
}

.gform_wrapper.gravity-theme .contact-form input:not([type="submit"]),
.gform_wrapper.gravity-theme .contact-form select,
.gform_wrapper.gravity-theme .contact-form textarea,
.gform_wrapper.gravity-theme .contractors-form input:not([type="submit"]),
.gform_wrapper.gravity-theme .contractors-form select,
.gform_wrapper.gravity-theme .contractors-form textarea {
	border-radius: 6px;
	border-color: var(--global-palette6);
	padding: 0.875rem;
}

.gform_wrapper.gravity-theme .contact-form .ginput_address_zip {
	flex: 0 0 100%;
}

.gform_wrapper.gravity-theme .contact-form .span-half {
	grid-column: span 6;
}

.gform_wrapper.gravity-theme .contact-form .gform_fields {
	grid-row-gap: 2rem;
}

.gform_wrapper.gravity-theme .contact-form:not(.request-info) .select-cond.span-full {
	grid-column: span 12;
}

@media (max-width: 640px) {
	.gform_wrapper.gravity-theme .contact-form .span-half {
		grid-column: span 12;
	}
}

/* Resource Library and Distributors resources S&F Query */

.resource-library-filters .search-filter-field--type-choice .search-filter-field__input.search-filter-input-group,
.distributors-filters .search-filter-field--type-choice .search-filter-field__input.search-filter-input-group {
	flex-direction: column;
	gap: 0;
	flex-wrap: nowrap;
}

.resource-library-filters .search-filter-field--type-choice .search-filter-label,
.distributors-filters .search-filter-field--type-choice .search-filter-label {
	color: var(--global-palette1);
}

.resource-library-filters .search-filter-input-checkbox__count,
.distributors-filters .search-filter-input-checkbox__count {
	flex-basis: 60px;
}

.resource-library-results .video-popup,
.distributors-results .video-popup {
	display: none;
	position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 50;
    min-height: 100vh;
    align-items: center;
    justify-content: center;
}

.resource-library-results .video-popup .popup-bg,
.distributors-results .video-popup .popup-bg {
	background: rgba(0,0,0,.7);
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: -1;
}

.resource-library-results .video-popup .popup-content,
.distributors-results .video-popup .popup-content {
	justify-self: center;
    height: 100%;
    align-content: center;
}

.resource-library-results .video-popup .close-btn,
.distributors-results .video-popup .close-btn {
	position: absolute;
    padding: 8px;
    font-size: 0.875rem;
    text-transform: capitalize;
    font-weight: 400;
    border-radius: 4px;
    margin-top: -40px;
    margin-left: -52px;
}

.resource-library-results ul.resources-wrap,
.distributors-results ul.resources-wrap {
	list-style-type: none;
	padding-left: 0;
	display: grid;
    grid-template-columns: 1fr;
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
}

.resource-library-results li.resource-information a,
.distributors-results li.resource-information a {
	display: block;
	color: #000;
	background-color: transparent;
	align-items: center;
    padding: 8px 16px;
	border: 1px solid #000;
    border-radius: 4px;
	text-decoration: none;
}

.resource-library-results li.resource-information a .category,
.distributors-results li.resource-information a .category {
	font-weight: 700;
	padding-bottom: 4px;
}

.resource-library-results li.resource-information a:hover,
.distributors-results li.resource-information a:hover {
	background-color: #f2f2f2;
	text-decoration: underline;
}

.resource-library-results li.resource-information a span,
.distributors-results li.resource-information a span {
	display: inline-block;
}

.resource-library-results li.resource-information a span,
.resource-library-results li.resource-information a span img,
.distributors-results li.resource-information a span,
.distributors-results li.resource-information a span img {
	height: auto;
	width: 20px;
	min-width: 20px
}

.resource-library-results .resources-pagination.bottom .wp-pagenavi span,
.resource-library-results .resources-pagination.bottom .wp-pagenavi a,
.distributors-results .resources-pagination.bottom .wp-pagenavi span,
.distributors-results .resources-pagination.bottom .wp-pagenavi a {
	border: none;
}

.resources-pagination.bottom .wp-pagenavi .pages,
.distributors-pagination.bottom .wp-pagenavi .pages {
	font-size: 0.875rem;
	margin-right: 1rem;
}

@media (max-width: 768px) {
	.resource-library-results ul.resources-wrap,
	.distributors-results ul.resources-wrap {

	}
	
	.resource-library-results .video-popup .popup-content,
	.distributors-results .video-popup .popup-content {
		width: 90%;
	}
	
	.resource-library-results .video-popup .popup-content iframe,
	.distributors-results .video-popup .popup-content iframe {
		width: 100%;
	}
	
}

/* Removing the underline on the links in the footer */

body footer.kb-row-layout-wrap .kb-navigation a {
	text-decoration: none!important;
}

body footer.kb-row-layout-wrap .kb-navigation a:hover {
	text-decoration: underline!important;
}

/* Fix for 100% column height */

.column-height-100 .kt-inside-inner-col {
	height: 100%;
}

.height-100 {
	height: 100%;
}

/* Reusable flex-grow class */

.flex-grow-1 {
	flex-grow: 1;
}

/* News Carousel slide height */

.news-carousel .kt-blocks-post-grid-item {
	height: 100%;
}

.blue-bold-txt strong {
	color: var(--global-palette2);
}

.dp-accordion {
	margin-bottom: 1rem;
	border: 1px solid #ccc;
	border-radius: 4px;
	overflow: hidden;
}
.dp-toggle {
	width: 100%;
	text-align: left;
	background: #f1f1f1;
	border: none;
	padding: 1rem;
	cursor: pointer;
	font-weight: bold;
	font-size: 1rem;
	transition: background 0.3s ease;
}
.dp-toggle.active {
	background: #e0e0e0;
}
.dp-panel {
	display: none;
	padding: 1rem;
	background: #fff;
	border-top: 1px solid #ccc;
}

/* Home Hero section stylings */

.home-hero-section .kb-blocks-bg-video-container {
	left: 40%;
}

.home-hero-section .kb-blocks-bg-video-container .kb-blocks-bg-video {
	object-fit: contain;
	object-position: bottom right;
}

.home-hero-section  h1.home-hero-gradient {
  background: -webkit-linear-gradient(#00306f 0%, #00306f 33%, #004c97 33%, #004c97 68%, #0084d5 68%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.home-hero-section h1.home-hero-gradient mark {
	color: #0084d5;
	font-size: 2rem;
	vertical-align: super;
}

@media (max-width: 1025px) {
	.home-hero-section .kb-blocks-bg-video-container {
		top: 60%;
		left: 0;
	}

	.home-hero-section .kb-blocks-bg-video-container .kb-blocks-bg-video {
		object-fit: cover;
		object-position: bottom center;
	}
}

@media (max-width: 768px) {
	.home-hero-section  h1.home-hero-gradient {
	 	background: -webkit-linear-gradient(#00306f 0%, #00306f 33%, #004c97 33%, #004c97 65%, #0084d5 65%);
		-webkit-background-clip: text;
 		-webkit-text-fill-color: transparent;
	}
}