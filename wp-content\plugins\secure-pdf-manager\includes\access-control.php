<?php
// access-control.php

function secure_pdf_user_has_access($folder) {
    if (!is_user_logged_in()) {
        return false;
    }

    $user = wp_get_current_user();
    $roles = (array) $user->roles;

    $folder_roles = get_option('secure_pdf_folders', []);

    if (!isset($folder_roles[$folder])) {
        return false;
    }

    foreach ($roles as $role) {
        if (in_array($role, $folder_roles[$folder], true)) {
            return true;
        }
    }

    return false;
}
