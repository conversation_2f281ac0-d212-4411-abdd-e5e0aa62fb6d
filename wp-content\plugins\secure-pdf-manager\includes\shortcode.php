<?php
// shortcode.php

function secure_pdf_list_shortcode($atts) {
    if (!is_user_logged_in()) return '';

    $atts = shortcode_atts(['folder' => ''], $atts);
    $folder = sanitize_title($atts['folder']);
    if (!$folder || !secure_pdf_user_has_access($folder)) return '';

    $base_path = WP_CONTENT_DIR . '/protected-files/' . $folder;
    if (!file_exists($base_path)) return '';

    $files = array_diff(scandir($base_path), ['.', '..']);
    if (empty($files)) return '<p>No files available.</p>';

    $output = '<ul class="secure-pdf-list">';
    foreach ($files as $file) {
        $url = add_query_arg([
            'secure_pdf_download' => urlencode($file),
            'folder' => urlencode($folder)
        ], site_url());
        $output .= '<li><a href="' . esc_url($url) . '">' . esc_html($file) . '</a></li>';
    }
    $output .= '</ul>';

    return $output;
}
add_shortcode('secure_pdf_list', 'secure_pdf_list_shortcode');
