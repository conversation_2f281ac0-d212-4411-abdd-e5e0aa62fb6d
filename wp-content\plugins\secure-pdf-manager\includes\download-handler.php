<?php
// download-handler.php

add_action('init', function () {
    if (!empty($_GET['secure_pdf'])) {
        $decoded = base64_decode(sanitize_text_field($_GET['secure_pdf']));
        if (strpos($decoded, ':') === false) wp_die('Invalid request.');
        list($folder, $file) = explode(':', $decoded, 2);

        if (!function_exists('secure_pdf_user_has_access')) {
            require_once __DIR__ . '/access-control.php';
        }

        if (!secure_pdf_user_has_access($folder)) {
            wp_redirect( site_url('/account-login/?redirect_to=' . urlencode($_SERVER['REQUEST_URI'])) );
            exit;
        }

        $protected_path = WP_CONTENT_DIR . '/protected-files/' . $folder . '/' . $file;
        if (!file_exists($protected_path)) {
            wp_die('File not found.');
        }

        $mime = mime_content_type($protected_path);


        header('Content-Type:'.$mime);
        header('Content-Disposition: attachment; filename="' . $file . '"');
        header('Content-Length: ' . filesize($protected_path));
        readfile($protected_path);
        exit;
    }
});