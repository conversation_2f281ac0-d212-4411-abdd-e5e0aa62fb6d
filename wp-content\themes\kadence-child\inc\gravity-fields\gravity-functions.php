<?php
/* * Gravity Forms Custom Fields Functions
 * This file contains custom field classes and functions for Gravity Forms.
 */
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
// Load ACF Fields
require_once('azure-acf.php');

// Load custom field classes
require_once('class-gf-cp-number-field.php');
require_once('class-gf-company-name-field.php');
require_once('class-marley-api-helper.php');

/**
 * CP Number lookup handler
 */
function kadence_ajax_search_cp_number() {
    check_ajax_referer( 'cp_lookup', 'nonce' );

    $cp = isset( $_POST['cp_number'] )
        ? strtoupper( sanitize_text_field( wp_unslash( $_POST['cp_number'] ) ) )
        : '';

    if ( ! preg_match( '/^CP\d{7}$/', $cp ) ) {
        wp_send_json_error( 'Invalid CP format' );
    }

    $api = marley_api_client();

    // Lookup a CP number
    $result = $api->cp_lookup( $cp );

    if ( isset($result ) ) {
        wp_send_json_success( $result );
    }

    wp_send_json_error( 'Not found' );
}
add_action( 'wp_ajax_search_cp_number',        'kadence_ajax_search_cp_number' );
add_action( 'wp_ajax_nopriv_search_cp_number', 'kadence_ajax_search_cp_number' );


/**
 * Company Name lookup handler
 */
function kadence_ajax_search_company_name() {
    check_ajax_referer( 'cp_lookup', 'nonce' );

    $name = isset( $_POST['company_name'] )
        ? sanitize_text_field( wp_unslash( $_POST['company_name'] ) )
        : '';

    if ( strlen( $name ) < 3 ) {
        wp_send_json_error( 'Query too short' );
    }

     $api = marley_api_client();

    // Lookup a CP number
    $result = $api->company_search( $name );

    if ( isset($result ) ) {
        wp_send_json_success( $result );
    }

    wp_send_json_error( 'No companies' );
}
add_action( 'wp_ajax_search_company_name',        'kadence_ajax_search_company_name' );
add_action( 'wp_ajax_nopriv_search_company_name', 'kadence_ajax_search_company_name' );


/**
 * AJAX callback: check if contact exists via Marley API.
 */
function ajax_check_contact() {
    // Verify nonce
    check_ajax_referer( 'cp_lookup', 'nonce' );

    $email = isset( $_POST['email'] ) ? sanitize_email( wp_unslash( $_POST['email'] ) ) : '';
    if ( empty( $email ) ) {
        wp_send_json_error( 'Missing email' );
    }

    // Validate email format
    if ( ! is_email( $email ) ) {
        wp_send_json_error( 'Invalid email format' );
    }

    $api    = marley_api_client();
    $exists = $api->check_contact( $email, false );

    wp_send_json_success( [ 'exists' => (bool) $exists ] );
}
add_action( 'wp_ajax_check_contact',        'ajax_check_contact' );
add_action( 'wp_ajax_nopriv_check_contact', 'ajax_check_contact' );

function kadence_enqueue_lookup_styles() { 
  wp_enqueue_style('kadence-lookup-style', get_stylesheet_directory_uri() . '/inc/gravity-fields/css/cp-lookup.css', array(), '1.0'); 
}
add_action('wp_enqueue_scripts', 'kadence_enqueue_lookup_styles');


function gf_cp_enqueue_script() {

    $handle = 'kadence-cp-lookup';

    wp_enqueue_script(
        $handle,
        get_stylesheet_directory_uri() . '/inc/gravity-fields/js/cp-lookup.js',
        [ 'jquery' ],
        '1.0',
        true
    );

    wp_localize_script(
        $handle,
        'ajax_object',
        array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce'    => wp_create_nonce( 'cp_lookup' ),
        )
    );
    
}
add_action( 'wp_enqueue_scripts', 'gf_cp_enqueue_script' );

/**
 * Add an “Enable Azure Sync” checkbox to the Form Settings panel.
 */
add_filter( 'gform_form_settings_fields', 'add_azure_sync_toggle', 10, 2 );
function add_azure_sync_toggle( $fields, $form ) {
    // Use the correct group key "Form Options", not "Form Settings"
    $fields['Form Options']['fields']['azure_sync_enabled'] = 
    [
        'label'   => esc_html__( 'Sync with Azure API', 'txtdomain' ),
        'type'    => 'checkbox',
        'name'    => 'azure_sync_enabled',
        'tooltip' => esc_html__( 'Sync with Azure API', 'txtdomain' ),
        'choices' => [
            [
                'label' => esc_html__( 'Yes, sync this form to Azure API', 'txtdomain' ),
                'value' => '0',
                'name'  => 'kadence_azure_sync_enabled',
            ],
        ],
    ];

    $fields['Form Options']['fields']['advance_form_enabled'] = 
    [
        'label'   => esc_html__( 'Enable Advance Form', 'txtdomain' ),
        'type'    => 'checkbox',
        'name'    => 'advance_form_enabled',
        'tooltip' => esc_html__( 'Enable Advance Form', 'txtdomain' ),
        'choices' => [
            [
                'label' => esc_html__( 'Yes, Enable Advance Form', 'txtdomain' ),
                'value' => '0',
                'name'  => 'kadence_advance_form_enabled',
            ],
        ],
    ];

    return $fields;

}


/**
 * After a form is submitted, if “Sync with Azure API” is enabled on the form,
 * push the entry data to the Marley “CreateBoilerRegistration” endpoint.
 */
add_action( 'gform_after_submission', 'sync_entry_to_azure_registration', 10, 2 );
function sync_entry_to_azure_registration( $entry, $form ) {

    // Only run when Azure Sync is enabled on this form
    if ( rgar( $form, 'kadence_azure_sync_enabled' ) !== '1' ) {
        return;
    }

    $api = marley_api_client();

    // Homeowner address
    $homeownerAddress = [
        'addressLine'   => rgar( $entry, '10.1' ) ?? '',
        'addressLine2'  => rgar( $entry, '10.2' ) ?? '',
        'locality'      => rgar( $entry, '10.3' ) ?? '',
        'adminDistrict' => rgar( $entry, '10.4' ) ?? '',
        'countryRegion' => 'United States',
        'postalCode'    => rgar( $entry, '10.5' ) ?? '',
    ];

    // Company address
    $companyAddress = [
        'addressLine'   => rgar( $entry, '5.1' ) ?? '',
        'addressLine2'  => rgar( $entry, '5.2' ) ?? '',
        'locality'      => rgar( $entry, '5.3' ) ?? '',
        'adminDistrict' => rgar( $entry, '5.4' ) ?? '',
        'countryRegion' => 'United States',
        'postalCode'    => rgar( $entry, '5.5' ) ?? '',
    ];

    // Base registration data
    $registrationData = [
        'HomeownerName'            => rgar( $entry, '9.3' ) ?? '',
        'HomeownerLastName'        => rgar( $entry, '9.6' ) ?? '',
        'HomeownerMobile'          => rgar( $entry, '16' ) ?? '',
        'HomeownerEmail'           => rgar( $entry, '15' ) ?? '',
        'HomeownerAddressLine1'    => $homeownerAddress['addressLine'],
        'HomeownerAddressLine2'    => $homeownerAddress['addressLine2'],
        'HomeownerCity'            => $homeownerAddress['locality'],
        'HomeownerStateOrProvince' => $homeownerAddress['adminDistrict'],
        'HomeownerCountry'         => $homeownerAddress['countryRegion'],
        'HomeownerPostalCode'      => $homeownerAddress['postalCode'],
        'IsSubmitted'              => true,
    ];

    // Company / Account info
    $accountId = rgar( $entry, '13' ) ?? null;

    
    if ($accountId && $accountId != 'New')  {
        $registrationData['Account'] = sanitize_text_field( $accountId );
    } else {
        $registrationData['CompanyName']         = rgar( $entry, '4' ) ?? '';
        $registrationData['CompanyAddressLine1'] = $companyAddress['addressLine'];
        $registrationData['CompanyAddressLine2'] = $companyAddress['addressLine2'];
        $registrationData['CompanyCity']         = $companyAddress['locality'];
        $registrationData['CompanyState']        = $companyAddress['adminDistrict'];
        $registrationData['CompanyZip']          = $companyAddress['postalCode'];
        $registrationData['CompanyCountry']      = $companyAddress['countryRegion'];
    }

    // CP Number data
    $cpNumberData = [
        'CPNumber' => rgar( $entry, '17' ) ?? null,
    ];

    // Installation date
    if ( $inst = rgar( $entry, '8' ) ) {
        $cpNumberData['InstallationDate'] = sanitize_text_field( $inst );
    }

    // Images: decode list, fetch, upload, collect cdnUrls
    $raw_images = rgar( $entry, '7' ) ?? '';
    $urls       = json_decode( $raw_images, true );

    if ( is_array( $urls ) ) {
        foreach ( $urls as $index => $url ) {
            if ( $url ) {
                $resp = wp_remote_get( $url,[ 'sslverify' => false ] );
                if ( ! is_wp_error( $resp ) ) {
                    $bin       = wp_remote_retrieve_body( $resp );
                    $mime      = wp_remote_retrieve_header( $resp, 'content-type' );
                    $filename  = wp_basename( parse_url( $url, PHP_URL_PATH ) );
                    $container = wp_generate_uuid4();
                    $upload    = $api->upload_image( $container, base64_encode( $bin ), $mime, $filename );
                    if ( is_array( $upload ) ) {
                        foreach ( $upload as $file ) {
                            if ( ! empty( $file['cdnUrl'] ) ) {
                                $cpNumberData[ 'Image' . ( $index + 1 ) ] = esc_url_raw( $file['cdnUrl'] );
                            }
                        }
                    }
                }
            }
        }
    }

    $registrationData['CPNumbers'] = [ $cpNumberData ];

    //Adding registration data to the entry notes
    if ( class_exists( 'GFAPI' ) ) {
        GFAPI::add_note(
            $entry['id'],
            1,
            'Boiler Registration Data',
            wp_json_encode( $registrationData ),
            'notification',
            'info'
        );
    }
    
    // Call the Marley API
    $result = $api->register_boiler( $registrationData );

    if ( $result === false ) {
        GFCommon::log_debug( __METHOD__ . '(): Azure registration failed for entry ' . $entry['id'] );
        //Adding registration data to the entry notes
        if ( class_exists( 'GFAPI' ) ) {
            GFAPI::add_note(
                $entry['id'],
                1,
                'Boiler Registration Failed',
                'Boiler Registration Failed',
                'notification',
                'error'
            );
        }

    } else {
        
        //Adding registration data to the entry notes
        if ( class_exists( 'GFAPI' ) ) {
            GFAPI::add_note(
                $entry['id'],
                1,
                'Boiler Registration Successful',
                wp_json_encode( $result ),
                'notification',
                'success'
            );
        }

        //Updating the meta with the response
        gform_update_meta( $entry['id'], 'azure_registration_response', wp_json_encode( $result ) );
    }
}