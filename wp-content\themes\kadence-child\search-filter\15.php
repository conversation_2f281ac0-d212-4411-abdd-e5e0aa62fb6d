<?php
/**
 * Search & Filter Pro
 *
 * Product Resource Listing
 *  /resources
 *
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
if ( $query->have_posts() ) : 

	wp_enqueue_style( 'resource-style', get_stylesheet_directory_uri() . '/inc/res-lib/resources.css', array(), null );
  	wp_enqueue_script( 'video-js', get_stylesheet_directory_uri() . '/inc/res-lib/video-popup.js', array('jquery'),'1.0.0',true );?>
		
	<ul class="resources-wrap">
	
		<?php 
		
		$query = new WP_Query( $args );
		while ( $query->have_posts() ) : $query->the_post(); ?>

			<li class="resource-information">
				<?php $id = get_the_ID();

				//get categories for this post
				$terms = get_the_terms( $id, 'sales-rep-category' );
				$category_list = "";
				if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) :
					$term_names = wp_list_pluck( $terms, 'name' );
					$category_list = implode( ', ', $term_names );
				endif; 

	     		$resource_type = get_field('document_type', $id); // 'youtube', 'vimeo', 'url', 'file'
		?>

				<?php //Check resource file type
				if ( $fileupload = get_field( 'file_upload', $id ) ) : 
				?>
					<a href="<?php echo esc_url($fileupload); ?>" target="_blank" class="resource-link">
						<div class="category"><?php echo esc_html( $category_list); ?></div>
						<?php echo esc_html( get_the_title() ); ?> 
						<span class="pdf-icon"><img src="<?php echo esc_url( get_stylesheet_directory_uri() ); ?>/inc/assets/img/PDF_24.png" alt="PDF" /></span>
					</a>

				<?php elseif ( get_field( 'external_url', $id ) ) :
					$ext_url  = get_field( 'external_url', $id );
				?>
					<a href="<?php echo esc_url($ext_url); ?>" target="_blank" class="resource-link">
						<div class="category"><?php echo esc_html( $category_list); ?></div>
						<?php echo esc_html( get_the_title() ); ?> 
						<span class="alert-icon" title="You will be leaving the site">&#9888;</span>
					</a>
				<?php elseif ( get_field( 'youtube_url', $id ) ) :
					$video_url = get_field( 'youtube_url', $id );
					$embed_url = '';
					$video_type = ''; 

					// Handle Youtube Videos
					if ( preg_match( '/(?:youtu\.be\/|youtube\.com\/(?:watch\?v=|embed\/|v\/))([\w\-]{11})/', $video_url, $matches ) ) {
						$video_id = $matches[1];
						$embed_url = 'https://www.youtube.com/embed/' . $video_id;
						$video_type = 'YouTube Video';
					}
					// Handle Vimeo videos
					elseif ( preg_match( '/vimeo\.com\/(?:video\/)?(\d+)/', $video_url, $matches ) ) {
						$video_id = $matches[1];
						$embed_url = 'https://player.vimeo.com/video/' . $video_id;
						$video_type = 'Vimeo Video';
					}

					if ( !empty( $embed_url ) ) :
				?>
					<a href="#" class="video-lightbox rep-resource-link">
						<div class="category"><?php echo esc_html( $category_list); ?></div>
						<?php echo esc_html( get_the_title() ); ?>
						<span class="pdf-icon">
							<img src="<?php echo get_stylesheet_directory_uri(); ?>/inc/assets/img/Vimeo-Camera.svg" alt="<?php echo esc_attr( $video_type ); ?>" />
						</span>
					</a>

					<div class="video-popup">
						<div class="popup-bg"></div>
						<div class="popup-content">
							<iframe src="<?php echo esc_url( $embed_url ); ?>" width="640" height="480" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
							<button class="close-btn">close</button>
						</div>
					</div>
				<?php
					endif;
				endif;
				?>
			
			</li>
			
		<?php endwhile; ?>

	</ul>
	
	<div class="resources-pagination bottom">
		
        <?php
		if ( function_exists( 'wp_pagenavi' ) ) {
			echo '<br />';
			wp_pagenavi( array( 'query' => $query ) );
		}
		?>

	</div>
<?php else :
	echo 'No Resources Found';
endif; ?>