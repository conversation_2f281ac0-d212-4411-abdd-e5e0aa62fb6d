document.addEventListener("DOMContentLoaded", function () {
    const modal = document.getElementById("sf-video-modal");
    const iframe = document.getElementById("sf-video-frame");
    const closeBtn = document.querySelector(".sf-video-close");

    if (!modal || !iframe || !closeBtn) return;

    document.querySelectorAll(".sf-video-trigger").forEach(el => {
        el.addEventListener("click", function (e) {
            e.preventDefault();
            const videoUrl = this.getAttribute("data-video-url");

            if (videoUrl.includes("youtube.com") || videoUrl.includes("youtu.be")) {
                let videoId = videoUrl.split("v=")[1] || videoUrl.split("/").pop();
                iframe.src = "https://www.youtube.com/embed/" + videoId + "?autoplay=1";
                console.log(iframe.src);
            } else if (videoUrl.includes("vimeo.com")) {
                let videoId = videoUrl.split("/").pop();
                iframe.src = "https://player.vimeo.com/video/" + videoId + "?autoplay=1";
            }

            modal.style.display = "flex";
        });
    });

    closeBtn.addEventListener("click", () => {
        modal.style.display = "none";
        iframe.src = "";
    });

    window.addEventListener("click", (e) => {
        if (e.target === modal) {
            modal.style.display = "none";
            iframe.src = "";
        }
    });
});

document.addEventListener('DOMContentLoaded', function () {
	const toggles = document.querySelectorAll('.dp-toggle');
	toggles.forEach(btn => {
		btn.addEventListener('click', () => {
			const panel = btn.nextElementSibling;
			btn.classList.toggle('active');
			panel.style.display = panel.style.display === 'block' ? 'none' : 'block';
		});
	});
});
