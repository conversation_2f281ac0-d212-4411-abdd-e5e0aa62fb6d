<?php
if ( ! defined( 'ABSPATH' ) ) exit;
if ( ! isset( $query ) ) return;

if ( $query->have_posts() ) : 
	$paged = isset( $query->query['paged'] ) ? $query->query['paged'] : 1;
?>

<div class="sf-summary">
	Found <?php echo esc_html( $query->found_posts ); ?> Results - Page <?php echo esc_html( $paged ); ?> of <?php echo esc_html( $query->max_num_pages ); ?>
</div>

<div class="sf-results-grid">
	<?php while ( $query->have_posts() ) : $query->the_post(); 
	     $resource_type = get_field('resource_type'); // 'youtube', 'vimeo', 'url', 'file'
	     $video_url = get_field('video_url');
	     $resource_url = get_field('resource_url');
	     $resource_file = get_field('file');
	     $post_id = get_the_ID();
	     $thumbnail = get_the_post_thumbnail_url( $post_id, 'medium' ); // fallback default

	     if (strtolower($resource_type) === 'youtube' && $video_url) {
	         $thumbnail = getYouTubeThumbnailUrl($video_url); 
	     } else if (strtolower($resource_type) === 'vimeo' && $video_url) {
	         $thumbnail = getVimeoThumbnailUrl($video_url); 
	     }
	?>
	<div class="sf-card">
		<div class="sf-card-image">
			<?php if ( strtolower($resource_type) == 'youtube' ||  strtolower($resource_type) == 'vimeo') : ?>
				<a href="#" class="sf-video-trigger" data-video-url="<?php echo esc_url( $video_url ); ?>">
					<img src="<?php echo esc_url( $thumbnail ); ?>" alt="<?php echo esc_attr( get_the_title() ); ?>">
					<div class="sf-video-overlay">▶</div>
				</a>
                <?php elseif ( strtolower($resource_type) === 'url' ) : ?>
                    <a href="<?php echo esc_url( $resource_url ); ?>" target="_blank" class="sf-icon-link">
                        <span class="sf-icon-overlay">
                            <i class="fas fa-link"> </i>
                        </span>
                    </a>
                <?php elseif ( strtolower($resource_type) === 'file' ) : ?>
                    <a href="<?php echo esc_url( $resource_file ); ?>" download class="sf-icon-link">
                        <span class="sf-icon-overlay">
                            <i class="fas fa-file-download"></i>
                        </span>
                    </a>
                <?php endif; ?>
		</div>
        <div class="sf-card-content">
			<p class="sf-category">
            <?php 
                $terms = get_the_terms($post_id, 'training-resource-category');
            
                if ($terms && !is_wp_error($terms)) {
                    echo '<p class="sf-category">';
                    $term_links = array_map(function($term) {
                        return esc_html($term->name);
                    }, $terms);
                    echo implode(', ', $term_links);
                    echo '</p>';
                }
                ?>
            </p>
			<h3 class="sf-title"><?php echo esc_html( get_the_title() ); ?></h3>
			<p class="sf-date"><?php echo esc_html( get_the_date() ); ?></p>
		</div>
	</div>
	<?php endwhile; ?>
</div>

<div class="sf-pagination">
	<?php if ( function_exists( 'searchandfilter_pagination' ) ) {
		searchandfilter_pagination();
	} else {
		the_posts_pagination();
	} ?>
</div>

<!-- Modal HTML for video -->
<div id="sf-video-modal" class="sf-video-modal" style="display: none;">
	<div class="sf-video-content">
		<span class="sf-video-close">&times;</span>
		<iframe id="sf-video-frame" src="" frameborder="0" allowfullscreen></iframe>
	</div>
</div>

<div class="resources-pagination bottom">		
    <?php
		if ( function_exists( 'wp_pagenavi' ) ) {
			echo '<br />';
			wp_pagenavi( array( 'query' => $query ) );
		}
	?>
</div>

<?php wp_reset_postdata(); else : ?>
	<p>No Results Found</p>
<?php endif; ?>
