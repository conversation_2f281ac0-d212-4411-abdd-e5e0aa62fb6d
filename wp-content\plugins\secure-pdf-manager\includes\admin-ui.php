<?php
// admin-ui.php

add_action('admin_menu', function () {
    add_menu_page(
        'Secure PDF Manager',
        'Secure PDFs',
        'manage_options',
        'secure-pdf-manager',
        'secure_pdf_render_admin_page',
        'dashicons-lock',
        25
    );
});

function secure_pdf_get_all_roles() {
    global $wp_roles;
    return $wp_roles->roles;
}

function secure_pdf_render_admin_page() {
    $folders = get_option('secure_pdf_folders', []);
    $all_roles = secure_pdf_get_all_roles();
    $selected_folder = $_POST['selected_folder'] ?? array_key_first($folders);

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && check_admin_referer('secure_pdf_manager')) {
        // Folder creation
        if (!empty($_POST['new_folder']) && isset($_POST['folder_roles']) && current_user_can('manage_options')) {
            $folder = sanitize_title($_POST['new_folder']);
            $roles = array_map('sanitize_text_field', $_POST['folder_roles']);
            if (!isset($folders[$folder])) {
                $folders[$folder] = $roles;
                wp_mkdir_p(WP_CONTENT_DIR . '/protected-files/' . $folder);
                update_option('secure_pdf_folders', $folders);
                echo '<div class="notice notice-success"><p>Folder created.</p></div>';
            }
        }

        // File upload
        if (!empty($_FILES['secure_file']['name']) && !empty($_POST['current_folder'])) {
            $folder = sanitize_title($_POST['current_folder']);
            $upload_dir = WP_CONTENT_DIR . '/protected-files/' . $folder . '/';
            if (!file_exists($upload_dir)) {
                wp_mkdir_p($upload_dir);
            }
            $target = $upload_dir . basename($_FILES['secure_file']['name']);

            $disallowed_exts = ['php', 'exe', 'js', 'sh', 'bat', 'pl', 'cgi', 'py', 'rb', 'asp', 'aspx'];

            $ext = strtolower(pathinfo($target, PATHINFO_EXTENSION));

            if (in_array($ext, $disallowed_exts)) {
                echo '<div class="notice notice-error"><p>This file type is not allowed for security reasons.</p></div>';
            } else {
                move_uploaded_file($_FILES['secure_file']['tmp_name'], $target);
                echo '<div class="notice notice-success"><p>File uploaded.</p></div>';
            }
        }

        // File delete
        if (!empty($_POST['delete_file']) && !empty($_POST['delete_folder'])) {
            $folder = sanitize_title($_POST['delete_folder']);
            $file = basename($_POST['delete_file']);
            $target = WP_CONTENT_DIR . '/protected-files/' . $folder . '/' . $file;
            if (file_exists($target)) {
                unlink($target);
                echo '<div class="notice notice-success"><p>File deleted.</p></div>';
            }
        }

        // Folder role update
        if (!empty($_POST['edit_folder']) && isset($_POST['updated_roles']) && current_user_can('manage_options')) {
            $folder = sanitize_title($_POST['edit_folder']);
            $roles = array_map('sanitize_text_field', $_POST['updated_roles']);
            if (isset($folders[$folder])) {
                $folders[$folder] = $roles;
                update_option('secure_pdf_folders', $folders);
                echo '<div class="notice notice-success"><p>Roles updated for folder.</p></div>';
            }
        }

        // Folder delete
        if (!empty($_POST['delete_folder_completely']) && current_user_can('manage_options')) {
            $delete_folder = sanitize_title($_POST['delete_folder_completely']);
            $folder_path = WP_CONTENT_DIR . '/protected-files/' . $delete_folder;
            if (isset($folders[$delete_folder])) {
                if (file_exists($folder_path)) {
                    $files = array_diff(scandir($folder_path), ['.', '..']);
                    foreach ($files as $file) {
                        @unlink($folder_path . '/' . $file);
                    }
                    @rmdir($folder_path);
                }
                unset($folders[$delete_folder]);
                update_option('secure_pdf_folders', $folders);
                echo '<div class="notice notice-success"><p>Folder and all files deleted.</p></div>';
                $selected_folder = array_key_first($folders);
            }
        }
    }
    ?>
    <div class="wrap">
        <h1>Secure PDF Manager</h1>

        <button class="button" onclick="document.getElementById('add-folder-form').style.display='block'; this.style.display='none';">+ Add Folder</button>
        <form method="post" id="add-folder-form" style="display:none; margin-top: 20px;">
            <?php wp_nonce_field('secure_pdf_manager'); ?>
            <h2>Create New Folder</h2>
            <input type="text" name="new_folder" placeholder="Folder slug" required>
            <br><br>
            <label>Select roles with access:</label><br>
            <select name="folder_roles[]" multiple required>
                <?php foreach ($all_roles as $role_key => $role): ?>
                    <option value="<?php echo esc_attr($role_key); ?>"><?php echo esc_html($role['name']); ?></option>
                <?php endforeach; ?>
            </select><br><br>
            <button class="button button-primary" type="submit">Create Folder</button>
        </form>

        <?php if (!empty($folders)) : ?>
            <hr>
            <h2>Select Folder to View Files</h2>
            <div class="secure-folder-controls" style="padding:15px;">
                <!-- Folder Select -->
                <div class="control-block" style="float:left;">
                    <form method="post">
                        <?php wp_nonce_field('secure_pdf_manager'); ?>
                        <label for="selected_folder" style="font-weight:bold;">Select Folder</label></br>
                        <select name="selected_folder" id="selected_folder" onchange="this.form.submit()">
                            <?php foreach ($folders as $folder => $roles): ?>
                                <option value="<?php echo esc_attr($folder); ?>" <?php selected($selected_folder, $folder); ?>>
                                    <?php echo esc_html($folder); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </form>
                    <!-- Folder Delete -->
                    <div>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete this folder and all its files?');">
                            <?php wp_nonce_field('secure_pdf_manager'); ?>
                            <input type="hidden" name="delete_folder_completely" value="<?php echo esc_attr($selected_folder); ?>">
                            <div style="margin:5px;"><button class="button button-danger" style="margin-top: 26px;">Delete Folder</button></div>
                        </form>
                    </div>
                </div>

                <!-- Role Editor -->
                <div class="control-block" style="float:left; padding-left:25px;">
                    <form method="post">
                        <?php wp_nonce_field('secure_pdf_manager'); ?>
                        <input type="hidden" name="edit_folder" value="<?php echo esc_attr($selected_folder); ?>">
                        <label for="updated_roles" style="font-weight:bold;">Allowed Roles</label></br>
                        <select name="updated_roles[]" multiple required>
                            <?php foreach ($all_roles as $role_key => $role): ?>
                                <option value="<?php echo esc_attr($role_key); ?>" <?php selected(in_array($role_key, $folders[$selected_folder]), true); ?>>
                                    <?php echo esc_html($role['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div style="margin:5px;"><button class="button button-secondary">Update Roles</button></div>
                    </form>
                </div>                
            </div>
        <?php endif; ?>

        <?php if (!empty($selected_folder) && isset($folders[$selected_folder])) :
            $folder_path = WP_CONTENT_DIR . '/protected-files/' . $selected_folder;
            $files = file_exists($folder_path) ? array_diff(scandir($folder_path), ['.', '..']) : [];
            ?>
            <hr style="clear:both;">
            <h2>Files in "<?php echo esc_html($selected_folder); ?>"</h2>

            

            <!-- File uploader -->
            <form method="post" enctype="multipart/form-data" style="margin-top:20px;clear:both;">
                <?php wp_nonce_field('secure_pdf_manager'); ?>
                <input type="hidden" name="current_folder" value="<?php echo esc_attr($selected_folder); ?>">
                <input type="file" name="secure_file" required>

                <button class="button button-secondary" type="submit">Upload File</button>
            </form>

            <?php if (!empty($files)) : ?>
                <table class="widefat striped" style="margin-top: 20px;">
                    <thead>
                        <tr>
                            <th>File</th>
                            <th>Uploaded / Date</th>
                            <th>Size</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($files as $file): 
                            $encoded = base64_encode($selected_folder . ':' . $file);
                            $url = site_url('?secure_pdf=' . urlencode($encoded));
                            ?>
                            <tr>
                                <td><a href="<?php echo esc_url($url); ?>" target="_blank"><?php echo esc_html($file); ?></a></td>
                                <td>System<br><?php echo date("Y-m-d H:i", filemtime($folder_path . '/' . $file)); ?></td>
                                <td><?php echo size_format(filesize($folder_path . '/' . $file), 2); ?></td>
                                <td>
                                    <form method="post" onsubmit="return confirm('Delete this file?');">
                                        <?php wp_nonce_field('secure_pdf_manager'); ?>
                                        <input type="hidden" name="delete_file" value="<?php echo esc_attr($file); ?>">
                                        <input type="hidden" name="delete_folder" value="<?php echo esc_attr($selected_folder); ?>">
                                        <button class="button button-small">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>No files found in this folder.</p>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php
}