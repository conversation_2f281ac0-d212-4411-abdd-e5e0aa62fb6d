<?php 

if (!defined('ABSPATH')) {
    exit; // Prevent direct access
}

class WM_Contractor_Locator {
    /**
     * API configuration array
     * @var array
     */
    private $api_config;

    public function __construct() {
        add_shortcode('wm_contractor_locator', [$this, 'render_locator']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_ajax_nopriv_wm_contractor_search', [$this, 'handle_ajax']);
        add_action('wp_ajax_wm_contractor_search', [$this, 'handle_ajax']);
    }

    public function render_locator() {
        ob_start();
        
        $template_path = get_stylesheet_directory() . '/partials/wm-locator-form.php';
        
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            echo '<div class="wm-error">Template file not found: ' . esc_html($template_path) . '</div>';
        }
        
        return ob_get_clean();
    }

    public function enqueue_assets() {
        wp_enqueue_script('mapbox-gl-js', 'https://cdn.jsdelivr.net/npm/mapbox-gl@1.8/dist/mapbox-gl.min.js', [], '1.8', true);
        wp_enqueue_script('wm-contractor-locator', get_stylesheet_directory_uri() . '/inc/wm-contractor-locator/wm-contractor-locator.js', ['jquery', 'mapbox-gl-js'], '1.0', true);
        
        wp_enqueue_style('wm-contractor-locator', get_stylesheet_directory_uri() . '/inc/wm-contractor-locator/wm-contractor-locator.css', [], '1.0');
        wp_enqueue_style('bootstrap-css', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css', [], '5.3.3');
        wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js', ['jquery'], '5.3.3', true);

        wp_localize_script('wm-contractor-locator', 'wmcl_ajax', [
            'ajax_url'   => admin_url('admin-ajax.php'),
            'nonce'      => wp_create_nonce('wm_contractor_locator_nonce'),
            'loading_gif'=> get_stylesheet_directory_uri() . '/inc/assets/img/wm-contractor-images/loading-cl.gif',
            'mapbox_key'  => $this->get_mapbox_api_key()
        ]);        
    }

    public function handle_ajax() {
        check_ajax_referer('wm_contractor_locator_nonce', 'security');

        $query = isset($_POST['query']) ? sanitize_text_field($_POST['query']) : '';
        $radius = isset($_POST['radius']) ? absint($_POST['radius']) : 10;
        $extra = isset($_POST['extra']) && is_array($_POST['extra']) ? array_map('sanitize_text_field', $_POST['extra']) : [];

        if (empty($query)) {
            wp_send_json_error(['message' => 'Empty query.']);
        }

        $html = $this->get_contractors($query, $radius, $extra);

        wp_send_json_success(['html' => $html]);
    }

    private function get_mapbox_api_key() {
        return 'pk.eyJ1IjoibWF0bW1wIiwiYSI6ImNsNm03eGd5aDBqMWQzaW1yaGRjcDBxaDMifQ.EPCl92_zuR3tnqmXXMOnew';
    }

    public function get_origins($query = '') {
        $origins = [];

        $env_path = plugin_dir_path(__FILE__) . '_env.php';
        if (file_exists($env_path)) {
            include $env_path;
        }

        if (!empty($query) && !empty($LOCON['bing-maps-origins-prod'])) {
            $bing_maps_key = $LOCON['bing-maps-origins-prod'];
            $url = 'http://dev.virtualearth.net/REST/v1/Locations/?q=' . urlencode($query) . '&o=json&key=' . $bing_maps_key;

            $response = wp_remote_get($url);

            if (is_array($response) && !is_wp_error($response)) {
                $body = wp_remote_retrieve_body($response);
                $decoded = json_decode($body, true);

                if (
                    isset($decoded['authenticationResultCode']) &&
                    $decoded['authenticationResultCode'] === 'ValidCredentials' &&
                    isset($decoded['resourceSets'][0]['resources'][0]['point']['coordinates'])
                ) {
                    $coords = $decoded['resourceSets'][0]['resources'][0]['point']['coordinates'];
                    $address = $decoded['resourceSets'][0]['resources'][0]['address'];

                    $origins['lat'] = $coords[0];
                    $origins['lon'] = $coords[1];
                    $origins['thezip'] = $address['postalCode'] ?? '';
                }
            }
        }

        return $origins;
    }

    public function use_token() {
        return $this->token_check('_n3k0t.json');
    }

    public function token_check($file_path = '') {
        $token = '';
        $current_time = time();
        $continue = true;

        $base_path = plugin_dir_path(__FILE__); 
        $file = $base_path . $file_path;

        if (file_exists($file)) {
            $last_modified = filemtime($file);
            $expire_time = 60 * 59;

            if ($current_time - $expire_time < $last_modified) {
                $response = file_get_contents($file);
                $decoded = json_decode($response, true);

                $token = $this->data_check($decoded, 'token');

                if (!empty($token)) {
                    $continue = false;
                }
            }
        }

        if ($continue === true) {
            $new_token = $this->generate_new_token();

            if (!empty($new_token)) {
                $token = $new_token;
                $data = [
                    'token' => $token,
                    'when'  => $current_time,
                ];

                $this->write_cache($data, $file_path);
            }
        }

        return $token;
    }

    public function data_check($arr = '', $key = '') {
        if (!empty($arr) && !empty($key)) {
            return isset($arr[$key]) ? $arr[$key] : '';
        }
        return '';
    }

    public function generate_new_token() {
        $token = '';

        $env_path = plugin_dir_path(__FILE__) . '_env.php';
        if (file_exists($env_path)) {
            include $env_path;
        }

        if (empty($LOCON)) {
            return '';
        }

        $url = $LOCON['token-url-prod'];
        $post_fields = [
            'grant_type'    => 'client_credentials',
            'client_secret' => $LOCON['token-client-secret-prod'],
            'client_id'     => $LOCON['token-client-id-prod'],
            'scope'         => $LOCON['token-scope-prod'],
        ];

        $args = [
            'method'      => 'POST',
            'body'        => $post_fields,
            'timeout'     => 15,
            'headers'     => [
                'Cookie' => 'fpc=At_vsLemfr9Fr0mhqQcwfeKpvnbdAQAAANcyjd0OAAAA; stsservicecookie=estsfd; x-ms-gateway-slice=estsfd'
            ]
        ];

        $response = wp_remote_post($url, $args);

        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $decoded = json_decode($body, true);

            $token = $this->data_check($decoded, 'access_token');
        }

        return $token;
    }

    public function write_cache($data = '', $file_path = '', $append = false) {
        if (empty($data) || empty($file_path)) {
            return;
        }

        $base_path = plugin_dir_path(__FILE__);
        $file = $base_path . $file_path;

        if ($append === true) {
            $contents = file_exists($file) ? file_get_contents($file) : '';
            $cont_arr = [];

            if (!empty($contents)) {
                $cont_arr = json_decode($contents, true);
            }

            $cont_arr[time()] = $data;

            file_put_contents($file, json_encode($cont_arr));
        } else {
            file_put_contents($file, json_encode($data));
        }
    }


    public function get_everything($token) {
        $keyed__arr = [];
        $destinations = [];

        $env_path = plugin_dir_path(__FILE__) . '_env.php';
        if (file_exists($env_path)) {
            include $env_path;
        }

        if (empty($LOCON['advantage-api-prod']) || empty($token)) {
            return [
                'keyed__arr'   => [],
                'destinations' => []
            ];
        }

        $url = $LOCON['advantage-api-prod'];

        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $token
            ]
        ]);

        if (is_wp_error($response)) {
            error_log('Request error: ' . $response->get_error_message());
            return [
                'keyed__arr'   => [],
                'destinations' => []
            ];
        }

        $everything = wp_remote_retrieve_body($response);

        $disallowed__names = [
            'DoNotApprove', 'ZZZZ', 'Brady Plumbing', 'Test Account', 'Test account', 'test account',
            'Quick Quote', 'Sample Account', 'Tech App', 'CGI Test', 'cgi test', '360 Test',
            'Wathier Plumbing', 'Weil McLain', 'Weil-Mclain', 'Wiabel', 'Z Golden', 'Zayre',
            'Yonkers', 'Willey Heating', 'Xander', 'Z Boyd', 'Z_360Insights', 'Z_cgi', 'Z_CGI',
            'Z_TEST', 'Z_Test', 'ZZZ Testing', 'Biewer Heating'
        ];

        if (!empty($everything)) {
            $all = json_decode($everything, true);

            if (!empty($all['accountDetails'])) {
                foreach ($all['accountDetails'] as $contractor) {
                    if ($this->issetnemp($contractor, 'lat') &&
                        $this->issetnemp($contractor, 'long') &&
                        $this->issetnemp($contractor, 'companyName')
                    ) {
                        if (!$this->str_contains($contractor['companyName'], $disallowed__names)) {
                            $key = $contractor['lat'] . $contractor['long'];
                            $keyed__arr[$key] = $contractor;
                            $destinations[] = [
                                'latitude'  => $contractor['lat'],
                                'longitude' => $contractor['long']
                            ];
                        }
                    }
                }
            }
        }
		
        return [
            'keyed__arr'   => $keyed__arr,
            'destinations' => $destinations
        ];
    }

    public function issetnemp($arr = [], $key = '') {
        return (!empty($arr) && !empty($key) && isset($arr[$key]) && !empty($arr[$key])) ? true : false;
    }

    public function str_contains($str, $arr) {
        foreach ($arr as $a) {
            if (stripos($str, $a) !== false) {
                return true;
            }
        }
        return false;
    }

    public function get_contractors() {
        $data = $_POST;
        $query = isset($data['query']) ? filter_var(trim($data['query']), FILTER_SANITIZE_STRING) : '';
        if (empty($query)) return;

        $radius = isset($data['radius']) ? filter_var(strip_tags(trim($data['radius'])), FILTER_SANITIZE_STRING) : '';
        $radius = (empty($radius) || !in_array($radius, ['5', '10', '25', '50', '75'], true)) ? 10 : intval($radius);

        $extra = isset($data['extra']) ? $data['extra'] : [];
        $all_filters = $extra;
        $all_filters[] = trim((string)$radius);

        $origins_all = $this->get_origins($query);
        $the_zip = $origins_all['thezip'] ?? '';
        $origins = [[
            'latitude'  => $origins_all['lat'] ?? 0,
            'longitude' => $origins_all['lon'] ?? 0,
        ]];

        $answer = [];

        if (!empty($origins)) {
            $everything = $this->get_everything($this->use_token());
            if (!empty($everything['keyed__arr']) && !empty($everything['destinations'])) {
                $env_path = plugin_dir_path(__FILE__) . '/_env.php';
				
                if (file_exists($env_path)) {
                    include $env_path;
                }
                $bing_maps_key = $LOCON['bing-maps-main-prod'];
                $keyed__arr = $everything['keyed__arr'];
                $destinations = $everything['destinations'];

                $post_data = [
                    "origins"      => $origins,
                    "destinations" => $destinations,
                    "travelMode"   => "driving",
                    "distanceUnit" => "mile",
                ];
                $json = json_encode($post_data);
                $response = wp_remote_post("https://dev.virtualearth.net/REST/v1/Routes/DistanceMatrix?key={$bing_maps_key}", [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Content-Length' => strlen($json),
                    ],
                    'body' => $json,
                ]);

                if (!is_wp_error($response)) {
                    $loc_matrix = wp_remote_retrieve_body($response);
                    $matrix_decoded = json_decode($loc_matrix, true);

                    if (!empty($matrix_decoded['resourceSets'][0]['resources'][0]['results'])) {
                        $results = $matrix_decoded['resourceSets'][0]['resources'][0]['results'];
                        $k_values = array_column($results, 'travelDistance');
                        array_multisort($k_values, SORT_ASC, $results);

                        foreach ($results as $loc) {
                            if ($loc['travelDistance'] !== -1 && $loc['travelDuration'] !== -1 && $loc['travelDistance'] <= $radius) {
                                $the_dest = $destinations[$loc['destinationIndex']];
                                $the_key = $the_dest['latitude'] . $the_dest['longitude'];
                                $new_arr = $keyed__arr[$the_key];

                                $new_arr['travelDistance'] = $loc['travelDistance'];
                                $new_arr['travelDuration'] = $loc['travelDuration'];

                                $tier_name = $new_arr['advantageProgramTIER'];
                                switch ($tier_name) {
                                    case 'Elite':
                                        $tier_number = '1';
                                        break;
                                    case 'Pro':
                                        $tier_number = '2';
                                        break;
                                    case 'Authorized':
                                        $tier_number = '3';
                                        break;
                                    default:
                                        $tier_number = '3';
                                        break;
                                }

                                $new_arr['tierNumber'] = $tier_number;
                                $answer[$the_key] = $new_arr;
                            }
                        }

                        if (!empty($answer)) {
                            $which_tier = array_column($answer, 'tierNumber');
                            $short_dist = array_column($answer, 'travelDuration');
                            array_multisort($which_tier, SORT_ASC, $short_dist, SORT_ASC, $answer);
                        }
                    }
                }
            }
        }

        if (empty($answer)) {
            $answer = ['error' => 'no results'];
        }

        if (!empty($extra)) {
            $answer = $this->filter_answers($extra, $answer);
        }

        if (empty($answer) || isset($answer['error'])) {
            $e_msg = 'No results. Please try another search.';
            if (empty($extra)) {
                $e_msg = ($radius < 75)
                    ? 'No results. Please try another search with a wider radius.'
                    : 'No results. Please try another search.';
            }

            $html = "<section class='pt-5 pb-5'><div class='container'><div class='row'><div class='col-sm-12 text-center'><p><strong>{$e_msg}</strong></p></div></div></div></section>";
            $html .= "<span id='cl-search-results' data-cls-time='" . time() . "' data-cls-search='" . esc_attr($query) . "' data-has-results='false' data-cls-filters='" . esc_attr(json_encode($all_filters)) . "'></span>";
            return wp_send_json_success(['html' => $html]);
        }

        $html = '<div id="results-outer"><div id="results-contained">';
        $count_elite = $count_pro = $count_auth = $the_count = $main_count = 0;
        $total_count = count($answer);
        $cls__names = [];

        foreach ($answer as $contractor) {
            $main_count++;
            $is_last = ($total_count === $main_count);

            $tier_name = $contractor['advantageProgramTIER'];
            switch ($tier_name) {
                case "Elite":      $count_elite++; $the_count = $count_elite; break;
                case "Pro":        $count_pro++;   $the_count = $count_pro;   break;
                case "Authorized": $count_auth++;  $the_count = $count_auth;  break;
            }

            if ($this->issetnemp($contractor, 'companyName')) {
                $cls__names[] = trim(htmlspecialchars($contractor['companyName'], ENT_QUOTES));
            }

            $html .= $this->build_html($contractor, (int)$the_count, $is_last, $count_pro, $the_zip);
        }

        $html .= '</div></div>';

        foreach ($answer as $contractor) {
            $html .= $this->build_modal($contractor, $the_zip);
            $html .= $this->generate_map_modal($contractor, $the_zip);
        }

        $html .= $this->generate_map_geojson($origins, $answer, $radius, $the_zip);

        $html .= "<span id='cl-search-results' data-cls-time='" . time() . "' data-cls-search='" . esc_attr($query) . "' data-has-results='true' data-cls-filters='" . esc_attr(json_encode($all_filters)) . "' data-cls-names='" . esc_attr(json_encode($cls__names)) . "'></span>";
        $html .= '<script>
            jQuery(function($) {
                $("[data-toggle=\'tooltip\']").each(function() {
                    var $element = $(this);
                    if ($element.length) {
                        $element.tooltip({
                            container: $element,
                            placement: "top",
                            template: \'<div class="custom-tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>\',
                            boundary: "viewport"
                        });
                    }
                });
            });
        </script>';
        
        return $html;
    }

    public function filter_answers($filters = [], $contractors = []) {
        if (!empty($filters)) {
            foreach ($filters as $filter) {
                if (in_array($filter, ['h247', 'bonded', 'financing', 'nate', 'spanish'], true)) {
                    foreach ($contractors as $k => $v) {
                        switch ($filter) {
                            case 'h247':
                                if (isset($v['emergencyService24Hrs']) && $v['emergencyService24Hrs'] === 'False') {
                                    unset($contractors[$k]);
                                }
                                break;

                            case 'bonded':
                                if (isset($v['bondedOrInsured']) && $v['bondedOrInsured'] === 'False') {
                                    unset($contractors[$k]);
                                }
                                break;

                            case 'financing':
                                if (isset($v['offerFinancing']) && $v['offerFinancing'] === 'False') {
                                    unset($contractors[$k]);
                                }
                                break;

                            case 'spanish':
                                if (empty($v['languages']) || strpos($v['languages'], 'Spanish') === false) {
                                    unset($contractors[$k]);
                                }
                                break;

                            case 'nate':
                                if (empty($v['certifications']) || strpos($v['certifications'], 'NATE') === false) {
                                    unset($contractors[$k]);
                                }
                                break;
                        }
                    }
                }
            }
        }

        return $contractors;
    }

    public function build_html($contractor = [], $position = 0, $is_last = false, $pro_count = 0, $the_zip = '') {
        $div = '';

        if (!empty($contractor)) {
            if ($this->issetnemp($contractor, 'advantageProgramTIER')) {
                $tier_name = $contractor['advantageProgramTIER'];

                switch ($tier_name) {
                    case "Elite":
                        $keyword = 'elite';
                        break;
                    case "Pro":
                        $keyword = 'pro';
                        break;
                    case "Authorized":
                        $keyword = 'authorized';
                        break;
                    default:
                        $keyword = 'authorized';
                }

                if ($keyword === 'authorized') {
                    $div = $this->build_authorized($contractor, $position, $is_last, $keyword, $pro_count, $the_zip);
                } else {
                    $div = $this->build_pro_elite($contractor, $position, $is_last, $keyword, $the_zip);
                }
            }
        }

        return $div;
    }

    public function build_authorized($contractor = [], $position = 0, $is_last = false, $keyword = '', $pro_count = 0, $the_zip = '') {
        $init_class = '';
        if (!empty($position) && $position === 1) {
            $init_class .= 'is--first ';
        }
        if (empty($pro_count)) {
            $init_class .= 'no--pro ';
        }

        $div = '';
        if ($position === 1) {
            $div .= "<section data-position='" . $position . "' class='cr--section {$init_class}pb-5 contain-{$keyword}'><div class='container no--padd'><div class='row row-lg-eq-height'>";
        }

        $contractor['the_zip'] = $the_zip;

        $which_tier = $this->part__tier($keyword, $contractor['advantageProgramTIER']);

        $travel_dist = isset($contractor['travelDistance']) ? round($contractor['travelDistance'], 1) : 0;
        $mile_or_miles = (intval($travel_dist) > 1 || empty($travel_dist)) ? ' miles' : ' mile';

        $distance_duration = "<div class='dist-dur'><span class='duration'>" . round($contractor['travelDuration'], 1) . " minutes</span><span class='distance'>" . $travel_dist . $mile_or_miles . "<span class='hide-mobile'> away</span></span></div>";

        $company_details   = $this->part__company($contractor);
        $company_links     = $this->part__links($contractor, 'links');
        $company_offerings = $this->part__offerings($contractor);

        $company_name = $this->issetnemp($contractor, 'companyName') ? $contractor['companyName'] : '';
        $company_rand = $this->issetnemp($contractor, 'accountNumber') ? $contractor['accountNumber'] : '';
        $company_id   = '';

        if ($this->issetnemp($contractor, 'lat') && $this->issetnemp($contractor, 'long')) {
            $company_id = $this->create_uqid($contractor['lat'], $contractor['long']);
        }

        $div .= "<div class='col-lg-6 pb-5 an-authorized'>";
        $div .= "<div class='a--contractor contractor-list inner-{$keyword} {$keyword}' data-conid='" . esc_attr($company_id) . "' data-conname='" . esc_attr($company_name) . "' data-contier='" . strtolower($contractor['advantageProgramTIER']) . "' data-conzip='" . esc_attr($contractor['zipCode']) . "' data-conadd='" . esc_attr($contractor['address1']) . "' data-conrand='" . esc_attr($company_rand) . "' data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>";

        $div .= "<div class='topbar'>{$which_tier}{$distance_duration}</div>";
        $div .= "<div class='ac-inner row'><div class='col-md-4'><div class='company-details'><img src='". get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images/tier-{$keyword}.png' class='ati' alt='" . esc_attr($contractor['advantageProgramTIER']) . "' /></div></div><div class='col-md-8'>{$company_details}{$company_links}</div><div class='col-md-12'>{$company_offerings}</div></div></div></div>";

        if ($is_last === true) {
            $div .= "</div></div><script>jQuery(document).ready(function($) {
                function equalHeight(group) {
                    var tallest = 0;
                    group.each(function() {
                        var thisHeight = $(this).height();
                        if (thisHeight > tallest) { tallest = thisHeight; }
                    });
                    group.height(tallest);
                }
                setTimeout(function(){ equalHeight($('.inner-authorized')); }, 1100);
                $(window).resize(function(){ equalHeight($('.inner-authorized')); });
            });</script></section>";
        }

        return $div;
    }

    public function build_modal($contractor, $the_zip = '') {
        if ($this->issetnemp($contractor, 'lat') && $this->issetnemp($contractor, 'long')) {
            $uqid = $this->create_uqid($contractor['lat'], $contractor['long']);
            $mid = str_replace(['-', '.'], '_', $contractor['lat'] . $contractor['long']);

            $tier = $contractor['advantageProgramTIER'];
            $keyword = strtolower($tier);
            $contractor['the_zip'] = $the_zip;

            $which_tier        = $this->part__tier($keyword, $contractor['advantageProgramTIER']);
            $company_details   = $this->part__company($contractor, false, false);
            $company_links     = $this->part__links($contractor, 'full-profile', true);
            $company_offerings = $this->part__offerings($contractor);
            $company_services  = $this->part__services($contractor);
            $company_fueltypes = $this->part__fueltypes($contractor);

            $modal  = "<div class='modal advntg-modal fade' id='m{$uqid}' tabindex='-1' role='dialog' aria-labelledby='modalLabel' aria-hidden='true' data-mmid='map_{$mid}'>";
            $modal .= "<div class='modal-dialog modal-lg' role='document'><div class='modal-content'>";
            $modal .= "<div class='modal-header'>";
            $modal .= "<button type='button' class='close' data-dismiss='modal' aria-label='Close' style='font-size:16px;font-weight:400'><span aria-hidden='true'>X</span></button>";
            $modal .= "<img src='". get_stylesheet_directory_uri() . "/inc/assets/img/wm-contractor-images/tier-{$keyword}.png' class='ati' alt='" . esc_attr($tier) . "' />";
            $modal .= "<h3 class='modal-title' id='modalLabel'>" . esc_html($contractor['companyName']) . "</h3>";
            $modal .= $which_tier . "</div>";

            $modal .= "<div class='modal-body'>";
            $modal .= "<div class='row first-row'>";
            $modal .= "<div class='col-md-5 col-lg-4'><div class='mmap' id='map_{$mid}'></div></div>";
            $modal .= "<div class='col-sm-7 col-lg-8 {$keyword}'>";
            $modal .= "<div class='row'><div class='col-sm-12'>{$company_details}{$company_offerings}</div></div>";
            $modal .= "<div class='row second-row'>";
            $modal .= "<div class='col-md-4'>{$company_links}</div>";
            $modal .= "<div class='col-md-4'>{$company_services}</div>";
            $modal .= "<div class='col-md-4'>{$company_fueltypes}</div>";
            $modal .= "</div>"; // second-row
            $modal .= "</div>"; // col right
            $modal .= "</div>"; // first-row
            $modal .= "</div>"; // modal-body

            $modal .= "</div></div>"; // modal-content, modal-dialog
            $modal .= "</div>"; // modal

            return $modal;
        }

        return '';
    }

    public function generate_map_modal($contractor, $the_zip = '') {
        $contractor_name = addslashes($contractor['companyName']);
        $contractor_lat  = $contractor['lat'];
        $contractor_lon  = $contractor['long'];
        $contractor_type = strtolower($contractor['advantageProgramTIER']);

        $uqid = $this->create_uqid($contractor_lat, $contractor_lon);
        $mid  = str_replace(['-', '.'], '_', $contractor_lat . $contractor_lon);

        ob_start();
        ?>
        <script>
        jQuery(document).ready(function($) {
            $('[data-target="#m<?php echo $uqid; ?>"]').on('click', function () {
                $('#m<?php echo $uqid; ?>').fadeIn(1);
                $('#m<?php echo $uqid; ?>').addClass('in');
                $('body').addClass('modal-open');

                $('<div />').appendTo('#m<?php echo $uqid; ?>').attr('class', 'modal-backdrop');

                $('#m<?php echo $uqid; ?> .close').on('click', function() {
                    $('#m<?php echo $uqid; ?>').fadeOut(1);
                    $('#m<?php echo $uqid; ?>').removeClass('in');
                    $('body').removeClass('modal-open');
                    $('#m<?php echo $uqid; ?> .modal-backdrop').remove();
                });

                $('#m<?php echo $uqid; ?> .modal-backdrop').on('click', function() {
                    $('#m<?php echo $uqid; ?> .close').trigger('click');
                });
                
                setTimeout(() => {
                    mapboxgl.accessToken = 'pk.eyJ1IjoibWF0bW1wIiwiYSI6ImNsNm03eGd5aDBqMWQzaW1yaGRjcDBxaDMifQ.EPCl92_zuR3tnqmXXMOnew';
                    const map<?php echo $mid; ?> = new mapboxgl.Map({
                        container: 'map_<?php echo $mid; ?>',
                        style: 'mapbox://styles/mapbox/streets-v12',
                        center: [<?php echo $contractor_lon; ?>, <?php echo $contractor_lat; ?>],
                        zoom: 16
                    });

                    map<?php echo $mid; ?>.addControl(new mapboxgl.NavigationControl(), 'bottom-right');
                    map<?php echo $mid; ?>.scrollZoom.disable();

                    const geojson2 = {
                        type: 'FeatureCollection',
                        features: [{
                            type: 'Feature',
                            geometry: {
                                type: 'Point',
                                coordinates: [<?php echo $contractor_lon; ?>, <?php echo $contractor_lat; ?>]
                            },
                            properties: {
                                title: '<?php echo $contractor_name; ?>',
                                type: '<?php echo $contractor_type; ?>',
                                pid: '<?php echo $mid; ?>'
                            }
                        }]
                    };

                    geojson2.features.forEach(function(marker) {
                        var el = document.createElement('div');
                        el.className = 'marker';
                        el.id = 'mapop' + marker.properties.pid;
                        el.style.backgroundImage = 'url(<?php echo get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images"; ?>/tier-' + marker.properties.type + '-pin.png)';
                        el.style.width = '40px';
                        el.style.height = '35px';
                        el.style.backgroundSize = 'cover';

                        new mapboxgl.Marker(el)
                            .setLngLat(marker.geometry.coordinates)
                            .setPopup(new mapboxgl.Popup({ offset: 25 })
                            .setHTML('<p><h6>' + marker.properties.title + '</h6></p>'))
                            .addTo(map<?php echo $mid; ?>);
                    });
                }, 501);
            });
        });
        </script>
        <?php
        return ob_get_clean();
    }

    public function generate_map_geojson($latlon, $results, $radius, $the_zip) {
        $reversed = array_reverse($results);
        ob_start();
        ?>
        <section class="map-contain mp--section is--closed" id="the-map-content">
        <div class="container">
            <div class="row the-map-row">
            <div class="the-map col-md-6 col-md-push-6 col-lg-7 col-lg-push-5">
                <div id="map"></div>
                <script>
                jQuery(document).ready(function($) {
                $('#the-map-content').fadeOut(1);
                $('#map').fadeOut(1);

                $('.request-appt').click(function(e){
                    e.preventDefault();
                    var uqid = $(this).attr('data-uqid');
                    console.log('uqid = ' + uqid);
                    alert('Request Appointment is not currently functional. This is here for demo purposes.');
                });

                function checkWidth<?php echo $radius; ?>() {
                    var z = <?php switch ($radius) {
                        case 5:
                            echo 10;
                            break;
                        case 10:
                            echo 9;
                            break;
                        case 25:
                            echo 8;
                            break;
                        case 50:
                            echo 7;
                            break;
                        case 75:
                            echo 7;
                            break;
                        default:
                            echo 9;
                            break;
                    } ?>;
                    if ($(window).width() < 800) {
                    z = <?php switch ($radius) {
                        case 5:
                            echo 8;
                            break;
                        case 10:
                            echo 7;
                            break;
                        case 25:
                            echo 6;
                            break;
                        case 50:
                            echo 6;
                            break;
                        case 75:
                            echo 5;
                            break;
                        default:
                            echo 7;
                            break;
                    } ?>;
                    }
                    return z;
                }

                function locon_draw_map() {
                    setTimeout(() => {
                    var zoom = checkWidth<?php echo $radius; ?>();
                    $(window).on('resize', function() {
                        zoom = checkWidth<?php echo $radius; ?>();
                    });

                    mapboxgl.accessToken = 'pk.eyJ1IjoibWF0bW1wIiwiYSI6ImNsNm03eGd5aDBqMWQzaW1yaGRjcDBxaDMifQ.EPCl92_zuR3tnqmXXMOnew';
                    const map = new mapboxgl.Map({
                        container: 'map',
                        style: 'mapbox://styles/mapbox/streets-v9',
                        center: [<?php echo $latlon[0]['longitude']; ?>, <?php echo $latlon[0]['latitude']; ?>],
                        zoom: zoom
                    });

                    map.addControl(new mapboxgl.NavigationControl(), 'bottom-right');
                    map.scrollZoom.disable();

                    var geojson2 = {
                        type: 'FeatureCollection',
                        features: [
                        <?php foreach ($reversed as $result): 
                            $contractor_name = addslashes($result['companyName']);
                            $contractor_lat  = $result['lat'];
                            $contractor_lon  = $result['long'];
                            $contractor_type = strtolower($result['advantageProgramTIER']);
                            $id = $contractor_lat . $contractor_lon;
                            $cid = 'c' . str_replace(['.', '-'], '', $contractor_lat . $contractor_lon);
                            $link = $this->part__expand_on_map($result, 'View Company Details');
                        ?>
                        {
                            type: 'Feature',
                            geometry: {
                            type: 'Point',
                            coordinates: [<?php echo $contractor_lon; ?>, <?php echo $contractor_lat; ?>]
                            },
                            properties: {
                            title: '<?php echo $contractor_name; ?>',
                            type: '<?php echo $contractor_type; ?>',
                            pid: '<?php echo $id; ?>',
                            cid: '<?php echo $cid; ?>',
                            link: "<?php echo addslashes($link); ?>"
                            }
                        },
                        <?php endforeach; ?>
                        ]
                    };

                    geojson2.features.forEach(function(marker) {
                        var el = document.createElement('div');
                        el.className = 'marker';
                        el.id = 'mapop' + marker.properties.pid;
                        el.style.backgroundImage = 'url(<?php echo get_stylesheet_directory_uri() . "/inc/assets/img/wm-contractor-images"; ?>/tier-' + marker.properties.type + '-pin.png)';
                        el.style.width = '70px';
                        el.style.height = '62px';
                        el.style.backgroundSize = 'cover';

                        new mapboxgl.Marker(el)
                        .setLngLat(marker.geometry.coordinates)
                        .setPopup(new mapboxgl.Popup({ offset: 25 })
                            .setHTML('<p><h6>' + marker.properties.title + '</h6></p><p>' + marker.properties.link + '</p>'))
                        .addTo(map);
                    });

                    $('.company-expand-link-on-map').on('click', function(e){
                        console.log('company-expand-link-on-map clicked');
                        e.preventDefault();
                        var mid = $(this).attr('data-mid');
                    });

                    }, 501);
                }

                $('.after--nav').click(function(e){
                    e.preventDefault();
                    var which = $(this).attr('data-which');
                    if (which === 'map') {
                    if ($('#the-map-content').hasClass('is--closed')) {
                        $('#results-outer .cr--section').fadeOut(1250);
                        $('#results-outer').addClass('is--closed');
                        $('#the-map-content').fadeIn(1500);
                        $('#map').fadeIn(1);
                        setTimeout(() => { locon_draw_map(); }, 101);
                        $('.after--nav').removeClass('active');
                        $(this).addClass('active');
                    }
                    }
                    if (which === 'listing') {
                    if ($('#results-outer').hasClass('is--closed')) {
                        $('#the-map-content').fadeOut(1250).addClass('is--closed');
                        $('#results-outer .cr--section').fadeIn(1500);
                        $('#results-outer').removeClass('is--closed');
                        $('.after--nav').removeClass('active');
                        $(this).addClass('active');
                    }
                    }
                });

                });
                </script>
                <script>
                    $('.company-expand-link').on('click', function(e){
                        console.log('company-expand-link clicked');
                        e.preventDefault();
                        var mid = $(this).attr('data-mid');
                    });
                </script>
            </div>
            <div class="col-md-6 col-md-pull-6 col-lg-5 col-lg-pull-7">
                <div class="row" id="results-with-map">
                <?php
                $r = 0;
                foreach ($results as $result) {
                    $r++;
                    echo $this->build_map_results($result, $r, $the_zip);
                }
                ?>
                </div>
            </div>
            </div>
        </div>
        </section>
        <?php
        return ob_get_clean();
    }

    public function build_pro_elite($contractor = [], $position = 0, $is_last = false, $keyword = '', $the_zip = '') {
        $init_class = '';
        if (!empty($position) && $position === 1) {
            $init_class .= 'is--first ';
        }
        if ($is_last === true) {
            $init_class .= 'is--last ';
        }

        $contractor['the_zip'] = $the_zip;

        $which_tier = $this->part__tier($keyword, $contractor['advantageProgramTIER']);
        $travel_dist = isset($contractor['travelDistance']) ? round($contractor['travelDistance'], 1) : 0;
        $mile_or_miles = (intval($travel_dist) > 1 || empty($travel_dist)) ? ' miles' : ' mile';

        $distance_duration = "<div class='dist-dur'><span class='duration'>" . round($contractor['travelDuration'], 1) . " minutes</span><span class='distance'>" . $travel_dist . $mile_or_miles . "<span class='hide-mobile'> away</span></span></div>";

        $company_details   = $this->part__company($contractor);
        $company_links     = $this->part__links($contractor, 'buttons', true);
        $company_offerings = $this->part__offerings($contractor);
        $company_services  = $this->part__services($contractor);
        $company_fueltypes = $this->part__fueltypes($contractor);

        $company_name = $this->issetnemp($contractor, 'companyName') ? $contractor['companyName'] : '';
        $company_rand = $this->issetnemp($contractor, 'accountNumber') ? $contractor['accountNumber'] : '';
        $company_id   = '';

        if ($this->issetnemp($contractor, 'lat') && $this->issetnemp($contractor, 'long')) {
            $company_id = $this->create_uqid($contractor['lat'], $contractor['long']);
        }

        $div  = "<section data-position='{$position}' class='cr--section {$init_class}pb-5 contain-{$keyword}'>";
        $div .= "<div class='container'><div class='row'>";
        $div .= "<div class='col-sm-12 a--contractor contractor-list inner-{$keyword} {$keyword}'";
        $div .= " data-conid='" . esc_attr($company_id) . "'";
        $div .= " data-conname='" . esc_attr($company_name) . "'";
        $div .= " data-contier='" . esc_attr(strtolower($contractor['advantageProgramTIER'])) . "'";
        $div .= " data-conzip='" . esc_attr($contractor['zipCode']) . "'";
        $div .= " data-conadd='" . esc_attr($contractor['address1']) . "'";
        $div .= " data-conrand='" . esc_attr($company_rand) . "'";
        $div .= " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>";

        $div .= "<div class='topbar'>{$which_tier}{$distance_duration}</div>";

        $div .= "<div class='ac-inner row'>";
        $div .= "<div class='col-md-7 brdr-rght'>";
        $div .= "<div class='company-details-contain'>";
        $div .= "<img src='". get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images/tier-{$keyword}.png' class='ati' alt='" . esc_attr($contractor['advantageProgramTIER']) . "' />";
        $div .= $company_details . $company_offerings;
        $div .= "</div></div>";

        $div .= "<div class='col-md-5 the-services brdr-lft'>";
        $div .= $company_services . $company_fueltypes;
        $div .= "</div><div></div></div></div></div>";

        $div .= "<div class='row bot-bar'><div class='col-md-12 bot-bar-col'>";
        $div .= $company_links;
        $div .= "</div></div></div></section>";

        return $div;
    }

    public function part__tier($keyword, $tier_name, $which = '') {
        $which_tier = '';

        if (!empty($keyword) && in_array($keyword, ['authorized', 'elite', 'pro'], true)) {
            $tooltip_placement = ($which === 'formap') ? ' data-placement="right"' : '';

            $stars = '';
            $star_img = get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images/star-{$keyword}.png";
            $alt = esc_attr($tier_name);

            switch ($keyword) {
                case 'authorized':
                    $stars = str_repeat("<img src='{$star_img}' alt='{$alt}' />", 3);
                    $title = "Authorized contractors have experience, education and training installing and servicing Weil-McLain products.";
                    break;

                case 'elite':
                    $stars = str_repeat("<img src='{$star_img}' alt='{$alt}' />", 5);
                    $title = "Elite contractors have the highest level of comprehensive experience, education and training installing Weil-McLain heating products. This is the most advanced Weil-McLain heating specialist in your service area.";
                    break;

                case 'pro':
                    $stars = str_repeat("<img src='{$star_img}' alt='{$alt}' />", 4);
                    $title = "Pro contractors have comprehensive experience, education and training installing and servicing Weil-McLain products.";
                    break;

                default:
                    $title = '';
            }

            $q_mark = "<img src='". get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images/q-mark.png' alt='about the {$keyword} tier' class='abt-tier' />";
            $q_mark_mobile = "<img src='". get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images/q-mark.png' alt='about the {$keyword} tier' class='abt-tier abt-tier-mobile' />";

            $which_tier = "<span class='whicht' data-toggle='tooltip'{$tooltip_placement} title='" . esc_attr($title) . "'>";
            $which_tier .= "<span class='tname'>" . esc_html($tier_name) . " {$q_mark_mobile}</span>";
            $which_tier .= "<span class='stars'>{$stars}</span>{$q_mark}</span>";
        }

        return $which_tier;
    }

    public function part__company($contractor, $show_name = true, $show_expanded = true) {
        $company_expanded = $this->part__expand($contractor);
        $details = "<div class='company-details'>";

        if (!empty($contractor)) {
            if ($show_name === true && $this->issetnemp($contractor, 'companyName')) {
                $details .= "<h3>" . esc_html($contractor['companyName']) . "</h3>";
            }

            if ($show_expanded === true && $this->is_pro_or_elite($contractor)) {
                $details .= $company_expanded;
            }

            $details .= "<div class='c-address'>";

            if ($this->issetnemp($contractor, 'address1')) {
                $details .= esc_html($contractor['address1']) . "<br />";
            }
            if ($this->issetnemp($contractor, 'address2')) {
                $details .= esc_html($contractor['address2']) . "<br />";
            }
            if ($this->issetnemp($contractor, 'city')) {
                $details .= esc_html($contractor['city']) . ", ";
            }
            if ($this->issetnemp($contractor, 'state')) {
                $details .= esc_html($contractor['state']) . " ";
            }
            if ($this->issetnemp($contractor, 'zipCode')) {
                $details .= esc_html($contractor['zipCode']);
            }

            $details .= "</div>";
        }

        $details .= "</div>";
        return $details;
    }

    public function part__links($contractor, $which = 'links', $request_quote = false) {
        $links = '';

        if ($which === 'links') {
            if ($this->issetnemp($contractor, 'businessPhone') || $this->issetnemp($contractor, 'website')) {
                $links .= "<div class='company-links'>";

                if ($this->issetnemp($contractor, 'businessPhone')) {
                    $links .= "<a href='tel:" . $this->convertphonenum($contractor['businessPhone'], 'link') . "' class='cl-phone'" .
                        " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                        " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                        " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                        " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                        " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>" .
                        $this->convertphonenum($contractor['businessPhone'], 'display') .
                        "</a>";
                }

                if ($this->issetnemp($contractor, 'website')) {
                    $links .= "<a href='" . esc_url($contractor['website']) . "' target='_blank'" .
                        " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                        " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                        " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                        " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                        " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>Visit Website</a>";
                }

                $links .= "</div>";
            }
        }

        if ($which === 'buttons') {
            if (!empty($contractor) && ($this->issetnemp($contractor, 'businessPhone') || $this->issetnemp($contractor, 'website'))) {
                $links .= "<div class='company-link-btns'>";

                if ($this->issetnemp($contractor, 'businessPhone')) {
                    $links .= "<button class='btn btn-primary btn-squ phone'><a href='tel:" . $this->convertphonenum($contractor['businessPhone'], 'link') . "' class='cl-phone'" .
                        " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                        " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                        " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                        " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                        " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>" .
                        $this->convertphonenum($contractor['businessPhone'], 'display') . " Call</a></button>";
                }

                if ($this->issetnemp($contractor, 'website')) {
                    $links .= "<button class='btn btn-primary btn-squ website'><a href='" . esc_url($contractor['website']) . "' target='_blank' rel='noopener'" .
                        " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                        " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                        " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                        " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                        " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>Website</a></button>";
                }

                $links .= "</div>";
            }
        }

        if ($which === 'full-profile') {
            $links .= "<div class='company-link-btns-full'>";

            if (!empty($contractor)) {
                if ($this->issetnemp($contractor, 'businessPhone')) {
                    $links .= "<button class='btn btn-primary btn-squ phone'><a href='tel:" . $this->convertphonenum($contractor['businessPhone'], 'link') . "' class='cl-phone'" .
                        " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                        " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                        " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                        " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                        " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>" .
                        $this->convertphonenum($contractor['businessPhone'], 'display') . " Call</a></button>";
                }

                if ($this->issetnemp($contractor, 'website')) {
                    $links .= "<button class='btn btn-primary btn-squ website'><a href='" . esc_url($contractor['website']) . "' target='_blank' rel='noopener'" .
                        " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                        " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                        " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                        " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                        " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>Website</a></button>";
                }
            }

            $links .= "</div>";
        }

        return $links;
    }

    public function part__offerings($contractor) {
        $offerings = "<div class='company-offerings'>";

        if (!empty($contractor)) {
            if ($this->issetnemp($contractor, 'emergencyService24Hrs') && $contractor['emergencyService24Hrs'] === 'True') {
                $offerings .= "<button class='btn btn-primary btn-squ'>24/7 Emergency Service</button>";
            }

            if ($this->issetnemp($contractor, 'bondedOrInsured') && $contractor['bondedOrInsured'] === 'True') {
                $offerings .= "<button class='btn btn-primary btn-squ'>Bonded & Insured</button>";
            }

            if ($this->issetnemp($contractor, 'offerFinancing') && $contractor['offerFinancing'] === 'True') {
                $offerings .= "<button class='btn btn-primary btn-squ'>Financing Available</button>";
            }

            if ($this->issetnemp($contractor, 'languages') && stripos($contractor['languages'], 'Spanish') !== false) {
                $offerings .= "<button class='btn btn-primary btn-squ'>Spanish Speaking</button>";
            }

            if ($this->issetnemp($contractor, 'certifications') && stripos($contractor['certifications'], 'NATE') !== false) {
                $offerings .= "<button class='btn btn-primary btn-squ'>NATE Certified</button>";
            }
        }

        $offerings .= "</div>";
        return $offerings;
    }

    public function create_uqid($lat, $lon) {
        $uqid = '';

        if (!empty($lat) && !empty($lon)) {
            $uqid = str_replace('.', '', $lat . $lon);
        }

        return $uqid;
    }

    public function part__services($contractor) {
        $services = "<div class='company-services'>";

        if (!empty($contractor) && $this->issetnemp($contractor, 'servicedInstalled')) {
            $services .= "<h4>Services Provided</h4><ul class='installed'>";
            $installed = explode(';', $contractor['servicedInstalled']);

            foreach ($installed as $li) {
                $li = trim($li);
                if (!empty($li)) {
                    $services .= "<li>" . esc_html($li) . "</li>";
                }
            }

            $services .= "</ul>";
        }

        $services .= "</div>";
        return $services;
    }

    public function part__fueltypes($contractor) {
        $services = "<div class='company-services'>";

        if (!empty($contractor) && $this->issetnemp($contractor, 'fuelTypes')) {
            $services .= "<h4>Fuel Types</h4><ul class='fueltypes'>";
            $fueltypes = explode(';', $contractor['fuelTypes']);

            foreach ($fueltypes as $li) {
                $li = trim($li);
                if (!empty($li)) {
                    $services .= "<li>" . esc_html($li) . "</li>";
                }
            }

            $services .= "</ul>";
        }

        $services .= "</div>";
        return $services;
    }

    public function part__expand($contractor, $link_text = 'View Company Details') {
        $link = '';

        if ($this->issetnemp($contractor, 'lat') && $this->issetnemp($contractor, 'long')) {
            $uqid = $this->create_uqid($contractor['lat'], $contractor['long']);
            $mid  = str_replace(['-', '.'], '_', $contractor['lat'] . $contractor['long']);

            $link = "<span class='co-expnd-lnk'><a href='#' class='company-expand-link' data-toggle='modal' data-target='#m" . esc_attr($uqid) . "' data-keyboard='true' data-backdrop='true' data-controls-modal='leave_modal'" .
                    " data-mid='" . esc_attr($mid) . "'" .
                    " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                    " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                    " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                    " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                    " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>" .
                    esc_html($link_text) . "</a></span>";
        }

        return $link;
    }

    public function part__expand_on_map($contractor, $link_text = 'View Company Details') {
        $link = '';

        if ($this->issetnemp($contractor, 'lat') && $this->issetnemp($contractor, 'long')) {
            $uqid = $this->create_uqid($contractor['lat'], $contractor['long']);
            $mid  = str_replace(['-', '.'], '_', $contractor['lat'] . $contractor['long']);

            $link = "<span class='co-expnd-lnk'><a href='#' class='company-expand-link-on-map' data-toggle='modal' data-target='#m" . esc_attr($uqid) . "' data-keyboard='true' data-backdrop='true' data-controls-modal='leave_modal'" .
                    " data-mid='" . esc_attr($mid) . "'" .
                    " data-conname='" . esc_attr($contractor['companyName']) . "'" .
                    " data-conzip='" . esc_attr($contractor['zipCode']) . "'" .
                    " data-conadd='" . esc_attr($contractor['address1']) . "'" .
                    " data-conrand='" . esc_attr($contractor['accountNumber']) . "'" .
                    " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>" .
                    esc_html($link_text) . "</a></span>";
        }

        return $link;
    }

    public function build_map_results($contractor = [], $position = 0, $the_zip = '') {
        $div = '';

        if (!empty($contractor)) {
            $contractor['the_zip'] = $the_zip;
            $init_class = '';

            $lat = strtolower($contractor['lat']);
            $lon = strtolower($contractor['long']);
            $cid = 'c' . str_replace(['.', '-'], '', (string)$lat . $lon);

            $keyword = strtolower($contractor['advantageProgramTIER']);

            $which_tier = $this->part__tier($keyword, $contractor['advantageProgramTIER'], 'formap');

            $travel_dist = isset($contractor['travelDistance']) ? round($contractor['travelDistance'], 1) : 0;
            $mile_or_miles = (intval($travel_dist) > 1 || empty($travel_dist)) ? ' miles' : ' mile';

            $distance_duration = "<div class='dist-dur'><span class='duration'>" . round($contractor['travelDuration'], 1) . " minutes</span><span class='distance'>" . $travel_dist . $mile_or_miles . "<span class='hide-mobile'> away</span></span></div>";

            $company_details   = $this->part__company($contractor);
            $company_links     = $this->part__links($contractor, 'buttons');
            $company_offerings = $this->part__offerings($contractor);
            $company_services  = $this->part__services($contractor);

            $the_bot_bar = empty($company_links) ? '' : "<div class='row bot-bar'><div class='col-md-12 bot-bar-col'>{$company_links}</div></div>";

            $company_name = $this->issetnemp($contractor, 'companyName') ? $contractor['companyName'] : '';
            $company_rand = $this->issetnemp($contractor, 'accountNumber') ? $contractor['accountNumber'] : '';
            $company_id = '';

            if ($this->issetnemp($contractor, 'lat') && $this->issetnemp($contractor, 'long')) {
                $company_id = $this->create_uqid($contractor['lat'], $contractor['long']);
            }

            if ($keyword !== 'n') {
                $div = "<section id='" . esc_attr($cid) . "' data-position='" . esc_attr($position) . "' class='cr--section {$init_class}pb-5 contain-{$keyword}'>";
                $div .= "<div class='container container-xs'><div class='row'><div class='col-sm-12 a--contractor contractor-map inner-{$keyword} {$keyword}'";
                $div .= " data-conid='" . esc_attr($company_id) . "'";
                $div .= " data-conname='" . esc_attr($company_name) . "'";
                $div .= " data-contier='" . esc_attr(strtolower($contractor['advantageProgramTIER'])) . "'";
                $div .= " data-conzip='" . esc_attr($contractor['zipCode']) . "'";
                $div .= " data-conadd='" . esc_attr($contractor['address1']) . "'";
                $div .= " data-conrand='" . esc_attr($company_rand) . "'";
                $div .= " data-consearchzip='" . esc_attr($contractor['the_zip']) . "'>";

                $div .= "<div class='topbar'>{$which_tier}{$distance_duration}</div>";
                $div .= "<div class='ac-inner row'>";
                $div .= "<div class='col-sm-12'><div class='company-details-contain'>";
                $div .= "<img src='". get_stylesheet_directory_uri() ."/inc/assets/img/wm-contractor-images/tier-{$keyword}.png' class='ati' alt='" . esc_attr($contractor['advantageProgramTIER']) . "' />";
                $div .= $company_details . $company_offerings;
                $div .= "</div></div>";
                $div .= "<div class='col-sm-12 the-services'>{$company_services}</div>";
                $div .= "<div></div></div></div></div>{$the_bot_bar}</div></section>";
            }
        }

        return $div;
    }

    public function is_pro_or_elite($contractor) {
        $answer = false;

        if ($this->issetnemp($contractor, 'advantageProgramTIER')) {
            $tier_name = strtolower($contractor['advantageProgramTIER']);
            if (in_array($tier_name, ['elite', 'pro'], true)) {
                $answer = true;
            }
        }

        return $answer;
    }

    public function convertphonenum($number = '', $which = 'link') {
        $number = preg_replace('/[^0-9]/', '', $number);

        if ($which === 'link') {
            return $number;
        } else {
            if (strlen($number) === 10) {
                return '(' . substr($number, 0, 3) . ') ' . substr($number, 3, 3) . '-' . substr($number, 6);
            }
            return $number;
        }
    }

}